import { CheckIcon, PlayIcon, CameraIcon, PencilIcon, ShareIcon, ChartBarIcon } from '@heroicons/react/24/outline'
import Link from 'next/link'

export default function ProdukcePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
      {/* Hero Section */}
      <section className="relative pt-20 pb-32 flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900"></div>
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%239C92AC" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="animate-fade-in-up">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-purple-500/20 border border-purple-500/30 mb-8">
              <span className="text-purple-300 text-sm font-medium">📱 Sociální sítě</span>
            </div>
            
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6">
              Produkce obsahu na
              <span className="block bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                sociální sítě
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-slate-300 mb-12 max-w-4xl mx-auto leading-relaxed">
              Kompletní řešení pro tvorbu a distribuci obsahu napříč všemi sociálními sítěmi. 
              Od natáčení po publikování - vše pod jednou střechou.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/kontakt" className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:from-purple-700 hover:to-pink-700 transition-all duration-300 shadow-lg hover:shadow-purple-500/25 hover:scale-105">
                Začít produkci
              </Link>
              <button className="border-2 border-purple-500 text-purple-300 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-purple-500/10 transition-all duration-300">
                Prohlédnout portfolio
              </button>
            </div>
          </div>
        </div>
        
        {/* Floating elements */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-purple-500/20 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-32 h-32 bg-pink-500/20 rounded-full blur-xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-blue-500/20 rounded-full blur-xl animate-pulse delay-500"></div>
      </section>

      {/* Services Overview */}
      <section className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center mb-20">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-200/50 mb-6">
              <CameraIcon className="w-4 h-4 text-blue-500" />
              <span className="text-sm font-medium text-blue-700">Kompletní produkce</span>
            </div>
            <h2 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-5xl font-space">
              Od nápadu po <span className="gradient-text">virální obsah</span>
            </h2>
            <p className="mt-6 text-xl leading-8 text-slate-600">
              Profesionální tým, který se postará o každý aspekt vaší online přítomnosti
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Tvorba obsahu */}
            <div className="group relative p-8 rounded-3xl bg-gradient-to-br from-purple-500 to-pink-500 shadow-2xl border border-white/30 overflow-hidden card-3d">
              <div className="absolute inset-0 opacity-5">
                <div className="absolute top-0 right-0 w-32 h-32 bg-white rounded-full -translate-y-16 translate-x-16"></div>
                <div className="absolute bottom-0 left-0 w-24 h-24 bg-white rounded-full translate-y-12 -translate-x-12"></div>
              </div>
              <div className="relative z-10">
                <div className="flex items-center gap-4 mb-6">
                  <div className="p-4 rounded-2xl bg-white/25 backdrop-blur-sm shadow-lg">
                    <PencilIcon className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-white">Tvorba obsahu</h3>
                    <p className="text-sm text-white/90 font-medium">Kreativní a strategický obsah</p>
                  </div>
                </div>
                <p className="text-white/95 mb-8 leading-relaxed font-medium">
                  Profesionální tvorba obsahu od nápadů přes scénáře až po finální podobu. 
                  Každý post je navržen pro maximální engagement.
                </p>
                <div className="mb-8">
                  <h4 className="font-bold text-white mb-4">Co zahrnujeme:</h4>
                  <div className="grid grid-cols-1 gap-3">
                    <div className="flex items-center text-sm text-white/90 font-medium">
                      <CheckIcon className="h-4 w-4 text-white mr-2 flex-shrink-0" />
                      <span>Strategické plánování obsahu</span>
                    </div>
                    <div className="flex items-center text-sm text-white/90 font-medium">
                      <CheckIcon className="h-4 w-4 text-white mr-2 flex-shrink-0" />
                      <span>Copywriting a scénáře</span>
                    </div>
                    <div className="flex items-center text-sm text-white/90 font-medium">
                      <CheckIcon className="h-4 w-4 text-white mr-2 flex-shrink-0" />
                      <span>Grafický design</span>
                    </div>
                    <div className="flex items-center text-sm text-white/90 font-medium">
                      <CheckIcon className="h-4 w-4 text-white mr-2 flex-shrink-0" />
                      <span>Video produkce</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Natáčení a produkce */}
            <div className="group relative p-8 rounded-3xl bg-gradient-to-br from-blue-500 to-cyan-500 shadow-2xl border border-white/30 overflow-hidden card-3d">
              <div className="absolute inset-0 opacity-5">
                <div className="absolute top-0 right-0 w-32 h-32 bg-white rounded-full -translate-y-16 translate-x-16"></div>
                <div className="absolute bottom-0 left-0 w-24 h-24 bg-white rounded-full translate-y-12 -translate-x-12"></div>
              </div>
              <div className="relative z-10">
                <div className="flex items-center gap-4 mb-6">
                  <div className="p-4 rounded-2xl bg-white/25 backdrop-blur-sm shadow-lg">
                    <CameraIcon className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-white">Natáčení & Produkce</h3>
                    <p className="text-sm text-white/90 font-medium">Profesionální video obsah</p>
                  </div>
                </div>
                <p className="text-white/95 mb-8 leading-relaxed font-medium">
                  Kompletní produkční tým s profesionální technikou pro vytvoření 
                  vysokoqualitního video obsahu pro všechny platformy.
                </p>
                <div className="mb-8">
                  <h4 className="font-bold text-white mb-4">Náš tým:</h4>
                  <div className="grid grid-cols-1 gap-3">
                    <div className="flex items-center text-sm text-white/90 font-medium">
                      <CheckIcon className="h-4 w-4 text-white mr-2 flex-shrink-0" />
                      <span>Kameraman a režisér</span>
                    </div>
                    <div className="flex items-center text-sm text-white/90 font-medium">
                      <CheckIcon className="h-4 w-4 text-white mr-2 flex-shrink-0" />
                      <span>Střihač a postprodukce</span>
                    </div>
                    <div className="flex items-center text-sm text-white/90 font-medium">
                      <CheckIcon className="h-4 w-4 text-white mr-2 flex-shrink-0" />
                      <span>Sound design</span>
                    </div>
                    <div className="flex items-center text-sm text-white/90 font-medium">
                      <CheckIcon className="h-4 w-4 text-white mr-2 flex-shrink-0" />
                      <span>Color grading</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Distribuce a management */}
            <div className="group relative p-8 rounded-3xl bg-gradient-to-br from-green-500 to-emerald-500 shadow-2xl border border-white/30 overflow-hidden card-3d">
              <div className="absolute inset-0 opacity-5">
                <div className="absolute top-0 right-0 w-32 h-32 bg-white rounded-full -translate-y-16 translate-x-16"></div>
                <div className="absolute bottom-0 left-0 w-24 h-24 bg-white rounded-full translate-y-12 -translate-x-12"></div>
              </div>
              <div className="relative z-10">
                <div className="flex items-center gap-4 mb-6">
                  <div className="p-4 rounded-2xl bg-white/25 backdrop-blur-sm shadow-lg">
                    <ShareIcon className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-white">Distribuce & Management</h3>
                    <p className="text-sm text-white/90 font-medium">Správa všech platforem</p>
                  </div>
                </div>
                <p className="text-white/95 mb-8 leading-relaxed font-medium">
                  Strategická distribuce obsahu napříč všemi sociálními sítěmi 
                  s optimalizací pro každou platformu zvlášť.
                </p>
                <div className="mb-8">
                  <h4 className="font-bold text-white mb-4">Platformy:</h4>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="flex items-center text-sm text-white/90 font-medium">
                      <CheckIcon className="h-4 w-4 text-white mr-2 flex-shrink-0" />
                      <span>Facebook</span>
                    </div>
                    <div className="flex items-center text-sm text-white/90 font-medium">
                      <CheckIcon className="h-4 w-4 text-white mr-2 flex-shrink-0" />
                      <span>Instagram</span>
                    </div>
                    <div className="flex items-center text-sm text-white/90 font-medium">
                      <CheckIcon className="h-4 w-4 text-white mr-2 flex-shrink-0" />
                      <span>TikTok</span>
                    </div>
                    <div className="flex items-center text-sm text-white/90 font-medium">
                      <CheckIcon className="h-4 w-4 text-white mr-2 flex-shrink-0" />
                      <span>YouTube</span>
                    </div>
                    <div className="flex items-center text-sm text-white/90 font-medium">
                      <CheckIcon className="h-4 w-4 text-white mr-2 flex-shrink-0" />
                      <span>LinkedIn</span>
                    </div>
                    <div className="flex items-center text-sm text-white/90 font-medium">
                      <CheckIcon className="h-4 w-4 text-white mr-2 flex-shrink-0" />
                      <span>X (Twitter)</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-24 sm:py-32 bg-slate-50/50">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center mb-20">
            <h2 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-5xl font-space">
              Jak <span className="gradient-text">funguje</span> naše produkce?
            </h2>
            <p className="mt-6 text-xl leading-8 text-slate-600">
              Systematický přístup k tvorbě obsahu, který garantuje konzistentní výsledky
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-xl">1</span>
              </div>
              <h3 className="text-xl font-semibold text-slate-900 mb-3">Strategie</h3>
              <p className="text-slate-600">Analýza cílové skupiny a vytvoření content strategie</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-xl">2</span>
              </div>
              <h3 className="text-xl font-semibold text-slate-900 mb-3">Produkce</h3>
              <p className="text-slate-600">Natáčení, fotografování a tvorba grafického obsahu</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-xl">3</span>
              </div>
              <h3 className="text-xl font-semibold text-slate-900 mb-3">Postprodukce</h3>
              <p className="text-slate-600">Střih, grafika, optimalizace pro jednotlivé platformy</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-xl">4</span>
              </div>
              <h3 className="text-xl font-semibold text-slate-900 mb-3">Distribuce</h3>
              <p className="text-slate-600">Publikování, monitoring a optimalizace výkonu</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative py-24 sm:py-32 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full blur-3xl float-animation"></div>
          <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-full blur-3xl float-animation" style={{animationDelay: '3s'}}></div>
        </div>
        
        <div className="relative z-10 mx-auto max-w-4xl px-6 lg:px-8 text-center">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 mb-8">
            <PlayIcon className="w-4 h-4 text-purple-300" />
            <span className="text-sm font-medium text-purple-200">Připraveni začít?</span>
          </div>
          
          <h2 className="text-4xl font-bold tracking-tight text-white sm:text-6xl font-space mb-6">
            Začněte svou <span className="gradient-text">content revoluci</span> ještě dnes
          </h2>
          
          <p className="mx-auto max-w-2xl text-xl leading-8 text-slate-300 mb-12">
            Získejte bezplatnou konzultaci a strategický plán pro vaši online přítomnost. 
            Během 30 minut vám ukážeme, jak zdvojnásobit váš dosah.
          </p>
          
          <div className="flex flex-col sm:flex-row items-center justify-center gap-6">
            <Link href="/kontakt" className="group relative px-8 py-4 text-lg font-semibold bg-white text-slate-900 rounded-2xl hover:bg-slate-100 transition-all duration-300 hover:scale-105 neon-glow">
              <span className="relative z-10 flex items-center gap-2">
                <CameraIcon className="w-5 h-5" />
                Bezplatná konzultace
              </span>
            </Link>
            <Link href="/o-nas" className="group flex items-center gap-2 px-6 py-3 text-lg font-medium text-white hover:text-purple-300 transition-colors">
              Prohlédnout portfolio
              <ChartBarIcon className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
