'use client'

import { useState } from 'react'
import Link from 'next/link'
import { 
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  ClockIcon,
  CheckCircleIcon,
  ArrowRightIcon,
  SparklesIcon
} from '@heroicons/react/24/outline'

const contactInfo = [
  {
    icon: EnvelopeIcon,
    title: 'Email',
    details: '<EMAIL>',
    description: 'Odpovídáme do 2 hodin'
  },
  {
    icon: PhoneIcon,
    title: 'Telefon',
    details: '+*********** 789',
    description: 'Po-Pá 9:00-18:00'
  },
  {
    icon: MapPinIcon,
    title: 'Adresa',
    details: 'Praha, Česká republika',
    description: 'Osobní schůzky po domluvě'
  },
  {
    icon: ClockIcon,
    title: '<PERSON>ych<PERSON><PERSON> odpověď',
    details: '30 minut',
    description: 'Průměrná doba odezvy'
  }
]

const services = [
  'Content distribuce',
  'Tvorba contentu',
  'Web & CRM automatizace',
  'SEO optimalizace',
  'Social media management',
  'Kompletní marketingové řešení'
]

export default function Kontakt() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    phone: '',
    service: '',
    message: '',
    budget: ''
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission here
    console.log('Form submitted:', formData)
    alert('Děkujeme za vaši zprávu! Ozveme se vám do 2 hodin.')
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
      {/* Hero section */}
      <div className="relative isolate px-6 pt-24 lg:px-8 hero-bg">
        <div className="absolute inset-0 -z-10 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full blur-3xl float-animation"></div>
          <div className="absolute top-3/4 right-1/4 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-cyan-400/20 rounded-full blur-3xl float-animation" style={{animationDelay: '2s'}}></div>
        </div>
        
        <div className="mx-auto max-w-4xl py-32 sm:py-48 lg:py-56 px-4">
          <div className="text-center">
            <div className="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-200/50 mb-8 glass">
              <SparklesIcon className="w-5 h-5 text-purple-600" />
              <span className="text-sm font-semibold text-purple-800">Začněme spolupráci</span>
            </div>
            
            <h1 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-6xl lg:text-7xl font-space leading-tight">
              Kontaktujte <span className="gradient-text">TK Nurture</span>
            </h1>
            
            <p className="mt-8 text-lg sm:text-xl leading-relaxed text-slate-600 max-w-3xl mx-auto">
              Získejte bezplatnou konzultaci a strategický plán pro váš podnik. 
              Během 30 minut vám ukážeme, jak můžete zdvojnásobit svůj online dosah.
            </p>
          </div>
        </div>
      </div>

      {/* Contact section */}
      <div className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
            {/* Contact form */}
            <div>
              <h2 className="text-3xl font-bold tracking-tight text-slate-900 sm:text-4xl font-space mb-8">
                Získejte <span className="gradient-text">bezplatnou konzultaci</span>
              </h2>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-slate-700 mb-2">
                      Jméno a příjmení *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      required
                      value={formData.name}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                      placeholder="Jan Novák"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-slate-700 mb-2">
                      Email *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      required
                      value={formData.email}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="company" className="block text-sm font-medium text-slate-700 mb-2">
                      Název firmy
                    </label>
                    <input
                      type="text"
                      id="company"
                      name="company"
                      value={formData.company}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                      placeholder="Moje firma s.r.o."
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-slate-700 mb-2">
                      Telefon
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                      placeholder="+*********** 789"
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="service" className="block text-sm font-medium text-slate-700 mb-2">
                      Jaká služba vás zajímá?
                    </label>
                    <select
                      id="service"
                      name="service"
                      value={formData.service}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                    >
                      <option value="">Vyberte službu</option>
                      {services.map((service) => (
                        <option key={service} value={service}>{service}</option>
                      ))}
                    </select>
                  </div>
                  
                  <div>
                    <label htmlFor="budget" className="block text-sm font-medium text-slate-700 mb-2">
                      Měsíční rozpočet
                    </label>
                    <select
                      id="budget"
                      name="budget"
                      value={formData.budget}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                    >
                      <option value="">Vyberte rozpočet</option>
                      <option value="10-25k">10 000 - 25 000 Kč</option>
                      <option value="25-50k">25 000 - 50 000 Kč</option>
                      <option value="50-100k">50 000 - 100 000 Kč</option>
                      <option value="100k+">100 000+ Kč</option>
                    </select>
                  </div>
                </div>
                
                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-slate-700 mb-2">
                    Zpráva
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    rows={4}
                    value={formData.message}
                    onChange={handleChange}
                    className="w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                    placeholder="Popište nám vaše potřeby a cíle..."
                  />
                </div>
                
                <button
                  type="submit"
                  className="w-full group relative px-8 py-4 text-lg font-semibold text-white rounded-2xl btn-futuristic neon-glow overflow-hidden shadow-xl"
                >
                  <SparklesIcon className="w-5 h-5 inline mr-2" />
                  Získat bezplatnou konzultaci
                  <div className="absolute inset-0 shimmer-effect opacity-0 group-hover:opacity-100 transition-opacity"></div>
                </button>
              </form>
            </div>
            
            {/* Contact info */}
            <div>
              <h2 className="text-3xl font-bold tracking-tight text-slate-900 sm:text-4xl font-space mb-8">
                Kontaktní <span className="gradient-text">informace</span>
              </h2>
              
              <div className="space-y-6 mb-12">
                {contactInfo.map((info, index) => (
                  <div 
                    key={info.title}
                    className="flex items-start gap-4 p-6 rounded-2xl bg-white shadow-lg border border-slate-200/50 hover:border-purple-200/50 transition-all duration-300"
                    style={{animationDelay: `${index * 0.1}s`}}
                  >
                    <div className="p-3 rounded-xl bg-gradient-to-br from-purple-500 to-pink-500">
                      <info.icon className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-slate-900 mb-1">{info.title}</h3>
                      <p className="text-lg font-medium text-slate-700 mb-1">{info.details}</p>
                      <p className="text-sm text-slate-500">{info.description}</p>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="p-8 rounded-3xl bg-gradient-to-br from-slate-900 to-slate-800 text-white">
                <h3 className="text-xl font-bold mb-6">Co můžete očekávat?</h3>
                
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <CheckCircleIcon className="h-5 w-5 text-green-400 flex-shrink-0" />
                    <span className="text-sm">Bezplatnou 30minutovou konzultaci</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircleIcon className="h-5 w-5 text-green-400 flex-shrink-0" />
                    <span className="text-sm">Analýzu vašich současných procesů</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircleIcon className="h-5 w-5 text-green-400 flex-shrink-0" />
                    <span className="text-sm">Strategický plán pro růst</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircleIcon className="h-5 w-5 text-green-400 flex-shrink-0" />
                    <span className="text-sm">Konkrétní doporučení a next steps</span>
                  </div>
                </div>
                
                <div className="mt-8 p-4 rounded-xl bg-white/5 backdrop-blur-sm">
                  <p className="text-sm text-slate-300">
                    <strong className="text-white">Garance:</strong> Pokud během 90 dnů neuvidíte zlepšení, 
                    vrátíme vám 100% investice.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Back to home */}
      <div className="py-16 text-center">
        <Link 
          href="/" 
          className="group inline-flex items-center gap-2 px-8 py-4 text-lg font-medium text-slate-700 hover:text-purple-600 transition-colors border border-slate-300 rounded-2xl hover:border-purple-300"
        >
          Zpět na hlavní stránku
          <ArrowRightIcon className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
        </Link>
      </div>
    </div>
  )
}
