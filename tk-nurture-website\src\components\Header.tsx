'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline'

const navigation = [
  { name: '<PERSON><PERSON>', href: '/' },
  { name: '<PERSON><PERSON><PERSON><PERSON>', href: '#sluzby' },
  { name: 'O nás', href: '/o-nas' },
  { name: 'Konta<PERSON>', href: '#kontakt' },
]

export default function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  return (
    <header className="sticky top-0 z-50 bg-black/90 backdrop-blur-sm border-b border-slate-700/50">
      <nav className="mx-auto flex max-w-7xl items-center justify-between p-6 lg:px-8" aria-label="Global">
        <div className="flex lg:flex-1">
          <Link href="/" className="-m-1.5 p-1.5 group">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-slate-600 to-gray-700 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 border border-slate-500/50">
                <span className="text-slate-200 font-bold text-lg">TK</span>
              </div>
              <div>
                <span className="text-xl font-bold text-white font-space">TK Nurture</span>
                <div className="text-xs text-slate-400">Troup & Koubek</div>
              </div>
            </div>
          </Link>
        </div>
        <div className="flex lg:hidden">
          <button
            type="button"
            className="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-white"
            onClick={() => setMobileMenuOpen(true)}
          >
            <span className="sr-only">Otevřít hlavní menu</span>
            <Bars3Icon className="h-6 w-6" aria-hidden="true" />
          </button>
        </div>
        <div className="hidden lg:flex lg:gap-x-8">
          {navigation.map((item) => (
            <Link
              key={item.name}
              href={item.href}
              className="relative px-3 py-2 text-sm font-bold text-slate-200 hover:text-white transition-colors group"
            >
              {item.name}
              <div className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-slate-400 to-gray-300 group-hover:w-full transition-all duration-300"></div>
            </Link>
          ))}
        </div>
        <div className="hidden lg:flex lg:flex-1 lg:justify-end">
          <Link
            href="#kontakt"
            className="relative px-6 py-3 text-sm font-semibold text-black bg-gradient-to-r from-slate-200 to-gray-300 rounded-xl hover:from-slate-300 hover:to-gray-400 transition-all duration-300 hover:scale-105"
          >
            <span className="relative z-10">Získat nabídku</span>
          </Link>
        </div>
      </nav>
      
      {/* Mobile menu */}
      {mobileMenuOpen && (
        <div className="lg:hidden">
          <div className="fixed inset-0 z-50" />
          <div className="fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-black/95 backdrop-blur-sm px-6 py-6 sm:max-w-sm border-l border-slate-700/50">
            <div className="flex items-center justify-between">
              <Link href="/" className="-m-1.5 p-1.5">
                <span className="text-xl font-bold text-white">TK Nurture</span>
              </Link>
              <button
                type="button"
                className="-m-2.5 rounded-md p-2.5 text-slate-300"
                onClick={() => setMobileMenuOpen(false)}
              >
                <span className="sr-only">Zavřít menu</span>
                <XMarkIcon className="h-6 w-6" aria-hidden="true" />
              </button>
            </div>
            <div className="mt-6 flow-root">
              <div className="-my-6 divide-y divide-slate-700/50">
                <div className="space-y-2 py-6">
                  {navigation.map((item) => (
                    <Link
                      key={item.name}
                      href={item.href}
                      className="-mx-3 block rounded-lg px-3 py-2 text-base font-bold leading-7 text-slate-200 hover:bg-slate-800/50"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      {item.name}
                    </Link>
                  ))}
                </div>
                <div className="py-6">
                  <Link
                    href="#kontakt"
                    className="bg-gradient-to-r from-slate-200 to-gray-300 text-black px-6 py-3 rounded-lg font-semibold hover:from-slate-300 hover:to-gray-400 block text-center transition-all duration-300"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Získat nabídku
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </header>
  )
}
