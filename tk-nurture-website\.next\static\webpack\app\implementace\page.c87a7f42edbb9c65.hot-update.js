"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/implementace/page",{

/***/ "(app-pages-browser)/./src/app/implementace/page.tsx":
/*!***************************************!*\
  !*** ./src/app/implementace/page.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Implementace)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_GlobeAltIcon_RocketLaunchIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,GlobeAltIcon,RocketLaunchIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_GlobeAltIcon_RocketLaunchIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,GlobeAltIcon,RocketLaunchIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_GlobeAltIcon_RocketLaunchIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,GlobeAltIcon,RocketLaunchIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_GlobeAltIcon_RocketLaunchIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,GlobeAltIcon,RocketLaunchIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/RocketLaunchIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_GlobeAltIcon_RocketLaunchIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,GlobeAltIcon,RocketLaunchIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_GlobeAltIcon_RocketLaunchIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,GlobeAltIcon,RocketLaunchIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_GlobeAltIcon_RocketLaunchIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,GlobeAltIcon,RocketLaunchIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_GlobeAltIcon_RocketLaunchIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChartBarIcon,CheckCircleIcon,ClockIcon,GlobeAltIcon,RocketLaunchIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst implementationSteps = [\n    {\n        id: 1,\n        title: 'Strategická analýza',\n        subtitle: 'Definice cílů a kanálů',\n        description: 'Analyzujeme vaše podnikání, cílové skupiny a definujeme optimální distribuční kanály a automatizace.',\n        icon: _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_GlobeAltIcon_RocketLaunchIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        duration: '1-2 týdny',\n        deliverables: [\n            'Audit současného stavu',\n            'Analýza konkurence',\n            'Definice cílových skupin',\n            'Strategie distribučních kanálů',\n            'Plán automatizací'\n        ],\n        gradient: 'from-blue-500 to-cyan-500',\n        bgColor: 'bg-blue-50'\n    },\n    {\n        id: 2,\n        title: 'Technické založení',\n        subtitle: 'Účty a platformy',\n        description: 'Založíme a nastavíme všechny potřebné účty na sociálních sítích, webové nástroje a CRM systémy.',\n        icon: _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_GlobeAltIcon_RocketLaunchIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        duration: '1 týden',\n        deliverables: [\n            'Sociální média účty',\n            'Google Analytics & Search Console',\n            'CRM systém',\n            'Email marketing nástroje',\n            'Webové formuláře'\n        ],\n        gradient: 'from-purple-500 to-pink-500',\n        bgColor: 'bg-purple-50'\n    },\n    {\n        id: 3,\n        title: 'Tým specialistů',\n        subtitle: 'Přiřazení freelancerů',\n        description: 'Podle vašich potřeb a rozpočtu sestavíme tým freelancerů s různými balíčky služeb.',\n        icon: _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_GlobeAltIcon_RocketLaunchIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        duration: '3-5 dní',\n        deliverables: [\n            'Výběr vhodných specialistů',\n            'Cenové balíčky',\n            'Komunikační kanály',\n            'Harmonogram práce',\n            'Quality assurance'\n        ],\n        gradient: 'from-green-500 to-emerald-500',\n        bgColor: 'bg-green-50'\n    },\n    {\n        id: 4,\n        title: 'Spuštění a optimalizace',\n        subtitle: 'Go-live a monitoring',\n        description: 'Spustíme všechny systémy, zahájíme content produkci a kontinuálně optimalizujeme výsledky.',\n        icon: _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_GlobeAltIcon_RocketLaunchIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        duration: 'Ongoing',\n        deliverables: [\n            'Spuštění kampaní',\n            'Content kalendář',\n            'Monitoring výsledků',\n            'Měsíční reporty',\n            'Kontinuální optimalizace'\n        ],\n        gradient: 'from-orange-500 to-red-500',\n        bgColor: 'bg-orange-50'\n    }\n];\nconst pricingTiers = [\n    {\n        name: 'Starter',\n        price: '15 000',\n        period: 'Kč/měsíc',\n        description: 'Pro začínající podniky',\n        features: [\n            'Grafik (20h/měsíc)',\n            'Copywriter (15h/měsíc)',\n            'Social media management',\n            'Základní SEO',\n            'Měsíční report'\n        ],\n        gradient: 'from-blue-500 to-cyan-500',\n        popular: false\n    },\n    {\n        name: 'Professional',\n        price: '35 000',\n        period: 'Kč/měsíc',\n        description: 'Pro rostoucí firmy',\n        features: [\n            'Grafik (40h/měsíc)',\n            'Copywriter (30h/měsíc)',\n            'Video editor (20h/měsíc)',\n            'SEO specialist (15h/měsíc)',\n            'PPC kampaně',\n            'Týdenní reporty'\n        ],\n        gradient: 'from-purple-500 to-pink-500',\n        popular: true\n    },\n    {\n        name: 'Enterprise',\n        price: '75 000',\n        period: 'Kč/měsíc',\n        description: 'Pro etablované společnosti',\n        features: [\n            'Kompletní tým (120h/měsíc)',\n            'Kameraman + střihač',\n            'Právní poradenství',\n            'Pokročilé automatizace',\n            'Dedikovaný account manager',\n            'Denní monitoring'\n        ],\n        gradient: 'from-orange-500 to-red-500',\n        popular: false\n    }\n];\nfunction Implementace() {\n    _s();\n    const [activeStep, setActiveStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative isolate px-6 pt-24 lg:px-8 hero-bg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 -z-10 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full blur-3xl float-animation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-3/4 right-1/4 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-cyan-400/20 rounded-full blur-3xl float-animation\",\n                                style: {\n                                    animationDelay: '2s'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto max-w-4xl py-16 sm:py-24 lg:py-32 px-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-flex items-center gap-2 px-6 py-3 rounded-full bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-200/50 mb-8 glass\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_GlobeAltIcon_RocketLaunchIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-5 h-5 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-semibold text-purple-800\",\n                                            children: \"Implementačn\\xed proces\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold tracking-tight text-slate-900 sm:text-6xl lg:text-7xl font-space leading-tight\",\n                                    children: [\n                                        \"Cesta k \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"gradient-text\",\n                                            children: \"digit\\xe1ln\\xed transformaci\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-8 text-lg sm:text-xl leading-relaxed text-slate-600 max-w-3xl mx-auto\",\n                                    children: \"Detailn\\xed roadmapa implementace, kter\\xe1 v\\xe1s provede od anal\\xfdzy až po spuštěn\\xed kompletn\\xedho marketingov\\xe9ho ekosyst\\xe9mu s t\\xfdmem specialistů.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"py-24 sm:py-32 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-3xl text-center mb-20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold tracking-tight text-slate-900 sm:text-5xl font-space\",\n                                    children: [\n                                        \"4 kroky k \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"gradient-text\",\n                                            children: \"\\xfaspěchu\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 25\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-6 text-xl leading-8 text-slate-600\",\n                                    children: \"Systematick\\xfd př\\xedstup k implementaci, kter\\xfd garantuje v\\xfdsledky\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-blue-500 via-purple-500 via-green-500 to-orange-500 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-24\",\n                                    children: implementationSteps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative flex items-center \".concat(index % 2 === 0 ? 'justify-start' : 'justify-end'),\n                                            style: {\n                                                animationDelay: \"\".concat(index * 0.2, \"s\")\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1/2 \".concat(index % 2 === 0 ? 'pr-12' : 'pl-12'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"group relative p-8 rounded-3xl \".concat(step.bgColor, \" border border-slate-200/50 hover:border-purple-200/50 transition-all duration-500 cursor-pointer card-3d\"),\n                                                        onClick: ()=>setActiveStep(step.id),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 bg-gradient-to-br \".concat(step.gradient, \" opacity-0 group-hover:opacity-5 rounded-3xl transition-opacity duration-500\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative z-10\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-4 mb-6\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-12 h-12 bg-gradient-to-br \".concat(step.gradient, \" rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300\"),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(step.icon, {\n                                                                                    className: \"w-6 h-6 text-white\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                                    lineNumber: 206,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                                lineNumber: 205,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                        className: \"text-xl font-bold text-slate-900 group-hover:text-purple-700 transition-colors\",\n                                                                                        children: step.title\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                                        lineNumber: 209,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm font-medium text-slate-600\",\n                                                                                        children: step.subtitle\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                                        lineNumber: 212,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                                lineNumber: 208,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"ml-auto flex items-center gap-2 text-sm font-medium text-slate-500\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_GlobeAltIcon_RocketLaunchIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                                        lineNumber: 215,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    step.duration\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                                lineNumber: 214,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                        lineNumber: 204,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-slate-700 leading-relaxed mb-6\",\n                                                                        children: step.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                        lineNumber: 220,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    activeStep === step.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-3 animate-fadeIn\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-semibold text-slate-900 mb-3\",\n                                                                                children: \"V\\xfdstupy:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                                lineNumber: 226,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            step.deliverables.map((deliverable)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center text-sm text-slate-600\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_GlobeAltIcon_RocketLaunchIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                                            className: \"h-4 w-4 text-green-500 mr-2 flex-shrink-0\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                                            lineNumber: 229,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: deliverable\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                                            lineNumber: 230,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, deliverable, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                                    lineNumber: 228,\n                                                                                    columnNumber: 31\n                                                                                }, this))\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                        lineNumber: 225,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute left-1/2 transform -translate-x-1/2 w-6 h-6 bg-gradient-to-br \".concat(step.gradient, \" rounded-full border-4 border-white shadow-lg z-10\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full h-full rounded-full bg-white/20\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, step.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"py-24 sm:py-32 bg-slate-50/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-3xl text-center mb-20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold tracking-tight text-slate-900 sm:text-5xl font-space\",\n                                    children: [\n                                        \"Cenov\\xe9 \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"gradient-text\",\n                                            children: \"bal\\xedčky\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 22\n                                        }, this),\n                                        \" t\\xfdmu\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-6 text-xl leading-8 text-slate-600\",\n                                    children: \"Flexibiln\\xed řešen\\xed podle velikosti a potřeb vašeho podniku\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                            children: pricingTiers.map((tier, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"group relative p-8 rounded-3xl bg-white shadow-lg border transition-all duration-500 card-3d \".concat(tier.popular ? 'border-purple-200 ring-2 ring-purple-500/20 scale-105' : 'border-slate-200/50 hover:border-purple-200/50'),\n                                    style: {\n                                        animationDelay: \"\".concat(index * 0.1, \"s\")\n                                    },\n                                    children: [\n                                        tier.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -top-4 left-1/2 transform -translate-x-1/2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-sm font-semibold rounded-full\",\n                                                children: \"Nejpopul\\xe1rnějš\\xed\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br \".concat(tier.gradient, \" opacity-0 group-hover:opacity-5 rounded-3xl transition-opacity duration-500\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative z-10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl font-bold text-slate-900 mb-2\",\n                                                            children: tier.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                            lineNumber: 285,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-slate-600 mb-6\",\n                                                            children: tier.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-baseline justify-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-4xl font-bold gradient-text\",\n                                                                    children: tier.price\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                    lineNumber: 288,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-slate-600\",\n                                                                    children: tier.period\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                    lineNumber: 289,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4 mb-8\",\n                                                    children: tier.features.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-slate-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_GlobeAltIcon_RocketLaunchIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-green-500 mr-3 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                    lineNumber: 296,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: feature\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                    lineNumber: 297,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, feature, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/kontakt\",\n                                                    className: \"w-full group relative px-6 py-3 text-center font-semibold rounded-2xl transition-all duration-300 block \".concat(tier.popular ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:scale-105 neon-glow' : 'bg-slate-100 text-slate-700 hover:bg-slate-200'),\n                                                    children: \"Vybrat bal\\xedček\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, tier.name, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"py-24 sm:py-32 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-3xl text-center mb-20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold tracking-tight text-slate-900 sm:text-5xl font-space\",\n                                    children: [\n                                        \"Vizualizace \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"gradient-text\",\n                                            children: \"procesu\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 27\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-6 text-xl leading-8 text-slate-600\",\n                                    children: \"Jak vypad\\xe1 kompletn\\xed implementace v praxi\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-slate-900 mb-8\",\n                                            children: [\n                                                \"Od strategie k \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"gradient-text\",\n                                                    children: \"realizaci\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 32\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center text-white font-bold text-sm\",\n                                                            children: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold text-slate-900 mb-2\",\n                                                                    children: \"Strategick\\xe1 anal\\xfdza\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-slate-600 text-sm\",\n                                                                    children: \"Definujeme distribučn\\xed cesty, kan\\xe1ly a automatizace podle vašeho byznysu\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-bold text-sm\",\n                                                            children: \"2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold text-slate-900 mb-2\",\n                                                                    children: \"Technick\\xe9 nastaven\\xed\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-slate-600 text-sm\",\n                                                                    children: \"Založ\\xedme \\xfačty na všech platform\\xe1ch podle best practices\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                    lineNumber: 354,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-500 rounded-full flex items-center justify-center text-white font-bold text-sm\",\n                                                            children: \"3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold text-slate-900 mb-2\",\n                                                                    children: \"Sestaven\\xed t\\xfdmu\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                    lineNumber: 363,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-slate-600 text-sm\",\n                                                                    children: \"Přiřad\\xedme freelancery podle vašich potřeb a rozpočtu\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                    lineNumber: 364,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center text-white font-bold text-sm\",\n                                                            children: \"4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold text-slate-900 mb-2\",\n                                                                    children: \"Spuštěn\\xed a optimalizace\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                    lineNumber: 373,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-slate-600 text-sm\",\n                                                                    children: \"Zah\\xe1j\\xedme produkci a kontinu\\xe1lně optimalizujeme v\\xfdsledky\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                    lineNumber: 374,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                            lineNumber: 372,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-8 rounded-3xl bg-gradient-to-br from-slate-900 to-slate-800 text-white overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full blur-3xl -translate-y-32 translate-x-32\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative z-10\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold mb-6\",\n                                                        children: \"Garantovan\\xe9 v\\xfdsledky\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-6 mb-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-3xl font-bold gradient-text mb-2\",\n                                                                        children: \"300%\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                        lineNumber: 389,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-slate-300\",\n                                                                        children: \"Průměrn\\xfd růst dosahu\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                        lineNumber: 390,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-3xl font-bold gradient-text mb-2\",\n                                                                        children: \"90\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                        lineNumber: 393,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-slate-300\",\n                                                                        children: \"Dn\\xed do prvn\\xedch v\\xfdsledků\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                        lineNumber: 394,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                lineNumber: 392,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-3xl font-bold gradient-text mb-2\",\n                                                                        children: \"24/7\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                        lineNumber: 397,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-slate-300\",\n                                                                        children: \"Monitoring a podpora\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                        lineNumber: 398,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                lineNumber: 396,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-3xl font-bold gradient-text mb-2\",\n                                                                        children: \"100%\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                        lineNumber: 401,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-slate-300\",\n                                                                        children: \"Z\\xe1ruka spokojenosti\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                        lineNumber: 402,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 rounded-xl bg-white/5 backdrop-blur-sm\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    className: \"text-white\",\n                                                                    children: \"Garance:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                                    lineNumber: 408,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" Pokud během 90 dnů neuvid\\xedte zlepšen\\xed, vr\\xe1t\\xedme v\\xe1m 100% investice.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                lineNumber: 320,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative py-24 sm:py-32 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full blur-3xl float-animation\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                            lineNumber: 422,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 mx-auto max-w-4xl px-6 lg:px-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl font-bold tracking-tight text-white sm:text-6xl font-space mb-6\",\n                                children: [\n                                    \"Začněte svou \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"gradient-text\",\n                                        children: \"transformaci\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 26\n                                    }, this),\n                                    \" dnes\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mx-auto max-w-2xl text-xl leading-8 text-slate-300 mb-12\",\n                                children: \"Nečekejte na konkurenci. Z\\xedskejte n\\xe1skok s kompletn\\xedm marketingov\\xfdm ekosyst\\xe9mem.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row items-center justify-center gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/kontakt\",\n                                        className: \"group relative px-10 py-4 text-lg font-semibold bg-white text-slate-900 rounded-2xl hover:bg-slate-100 transition-all duration-300 hover:scale-105 neon-glow shadow-xl\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"relative z-10 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_GlobeAltIcon_RocketLaunchIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Zač\\xedt implementaci\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        className: \"group flex items-center gap-2 px-8 py-4 text-lg font-medium text-white hover:text-purple-300 transition-colors border border-white/20 rounded-2xl hover:border-purple-300\",\n                                        children: [\n                                            \"Zpět na hlavn\\xed str\\xe1nku\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_ClockIcon_GlobeAltIcon_RocketLaunchIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4 group-hover:translate-x-1 transition-transform\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                        lineNumber: 425,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n                lineNumber: 420,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\implementace\\\\page.tsx\",\n        lineNumber: 145,\n        columnNumber: 5\n    }, this);\n}\n_s(Implementace, \"iIYKRAP+e/JAhvMHZK3k37STQJ8=\");\n_c = Implementace;\nvar _c;\n$RefreshReg$(_c, \"Implementace\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/implementace/page.tsx\n"));

/***/ })

});