import Link from 'next/link'
import { 
  ComputerDesktopIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  CircleStackIcon,
  MagnifyingGlassIcon,
  CogIcon,
  CheckCircleIcon,
  ArrowRightIcon,
  SparklesIcon,
  BoltIcon
} from '@heroicons/react/24/outline'

const services = [
  {
    name: 'Cha<PERSON><PERSON><PERSON>',
    description: 'Inteligentní chatboty pro automatickou komunikaci s klienty 24/7',
    icon: ChatBubbleLeftRightIcon,
    features: ['AI-powered odpovědi', 'Multilingual podpora', 'Lead qualification', 'Integrace s CRM'],
    gradient: 'from-blue-500 to-cyan-500',
    stats: '90% rychlej<PERSON>í odpovědi'
  },
  {
    name: 'Formuláře',
    description: 'Optimalizované formuláře pro maximální konverze',
    icon: DocumentTextIcon,
    features: ['Smart validace', 'Multi-step formuláře', 'A/B testing', 'GDPR compliance'],
    gradient: 'from-green-500 to-emerald-500',
    stats: '40% vyš<PERSON><PERSON> kon<PERSON>'
  },
  {
    name: 'CRM Systémy',
    description: 'Kompletní správa vztahů se zákazníky',
    icon: CircleStackIcon,
    features: ['Lead management', 'Sales pipeline', 'Automatizace', 'Reporting'],
    gradient: 'from-purple-500 to-pink-500',
    stats: '60% efektivnější sales'
  },
  {
    name: 'Databáze',
    description: 'Robustní databázová řešení pro vaše data',
    icon: CircleStackIcon,
    features: ['Cloud storage', 'Backup & recovery', 'Škálovatelnost', 'Bezpečnost'],
    gradient: 'from-orange-500 to-red-500',
    stats: '99.9% uptime'
  },
  {
    name: 'Automatické inzeráty',
    description: 'AI-řízené reklamní kampaně pro maximální ROI',
    icon: MagnifyingGlassIcon,
    features: ['Smart bidding', 'Audience targeting', 'Creative optimization', 'Performance tracking'],
    gradient: 'from-yellow-500 to-amber-500',
    stats: '200% lepší ROI'
  },
  {
    name: 'Automatizace',
    description: 'Workflow automatizace pro efektivnější procesy',
    icon: CogIcon,
    features: ['Email marketing', 'Lead nurturing', 'Task automation', 'Integration hub'],
    gradient: 'from-indigo-500 to-purple-500',
    stats: '70% úspora času'
  }
]

const integrations = [
  { name: 'Salesforce', logo: '☁️' },
  { name: 'HubSpot', logo: '🧡' },
  { name: 'Mailchimp', logo: '🐵' },
  { name: 'Google Analytics', logo: '📊' },
  { name: 'Facebook Ads', logo: '📘' },
  { name: 'Google Ads', logo: '🔍' },
  { name: 'Zapier', logo: '⚡' },
  { name: 'Slack', logo: '💬' }
]

const benefits = [
  {
    title: 'Automatizace 24/7',
    description: 'Systémy pracují za vás i když spíte'
  },
  {
    title: 'Škálovatelnost',
    description: 'Roste s vaším podnikem bez omezení'
  },
  {
    title: 'Integrace',
    description: 'Propojení všech vašich nástrojů'
  },
  {
    title: 'Analytika',
    description: 'Detailní přehledy o výkonnosti'
  }
]

export default function WebCRM() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
      {/* Hero section */}
      <div className="relative isolate px-6 pt-24 lg:px-8 hero-bg">
        <div className="absolute inset-0 -z-10 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-green-400/20 to-emerald-400/20 rounded-full blur-3xl float-animation"></div>
          <div className="absolute top-3/4 right-1/4 w-80 h-80 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full blur-3xl float-animation" style={{animationDelay: '2s'}}></div>
        </div>
        
        <div className="mx-auto max-w-4xl py-32 sm:py-48 lg:py-56 px-4">
          <div className="text-center">
            <div className="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-gradient-to-r from-green-500/10 to-emerald-500/10 border border-green-200/50 mb-8 glass">
              <BoltIcon className="w-5 h-5 text-green-600" />
              <span className="text-sm font-semibold text-green-800">Automatizace & Integrace</span>
            </div>
            
            <h1 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-6xl lg:text-7xl font-space leading-tight">
              Web & <span className="gradient-text">CRM Automatizace</span>
            </h1>
            
            <p className="mt-8 text-lg sm:text-xl leading-relaxed text-slate-600 max-w-3xl mx-auto">
              Moderní webové řešení propojené s CRM systémy a automatizací, 
              které pracuje za vás 24/7 a škáluje s vaším podnikem.
            </p>
            
            <div className="mt-16 flex flex-col sm:flex-row items-center justify-center gap-6">
              <Link
                href="/kontakt"
                className="group relative px-10 py-4 text-lg font-semibold text-white rounded-2xl btn-futuristic neon-glow overflow-hidden shadow-xl"
              >
                <SparklesIcon className="w-5 h-5 inline mr-2" />
                Automatizovat procesy
              </Link>
              
              <Link 
                href="/" 
                className="group flex items-center gap-2 px-8 py-4 text-lg font-medium text-slate-700 hover:text-purple-600 transition-colors border border-slate-300 rounded-2xl hover:border-purple-300"
              >
                Zpět na hlavní stránku
                <ArrowRightIcon className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Services section */}
      <div className="py-24 sm:py-32 bg-slate-50/50">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center mb-20">
            <h2 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-5xl font-space">
              Inteligentní <span className="gradient-text">systémy</span> pro růst
            </h2>
            <p className="mt-6 text-xl leading-8 text-slate-600">
              Kompletní sada nástrojů pro automatizaci a optimalizaci vašeho podnikání
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <div 
                key={service.name} 
                className="group relative card-3d"
                style={{animationDelay: `${index * 0.1}s`}}
              >
                <div className={`relative h-full p-8 rounded-3xl bg-gradient-to-br ${service.gradient} shadow-2xl border border-white/30 overflow-hidden`}>
                  <div className="absolute inset-0 opacity-5">
                    <div className="absolute top-0 right-0 w-32 h-32 bg-white rounded-full -translate-y-16 translate-x-16"></div>
                  </div>
                  
                  <div className="relative z-10">
                    <div className="flex items-center gap-4 mb-6">
                      <div className="p-4 rounded-2xl bg-white/25 backdrop-blur-sm shadow-lg">
                        <service.icon className="h-8 w-8 text-white" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-white">{service.name}</h3>
                        <p className="text-sm text-white/90 font-medium">{service.stats}</p>
                      </div>
                    </div>
                    
                    <p className="text-white/95 mb-8 leading-relaxed font-medium">
                      {service.description}
                    </p>
                    
                    <div className="mb-8">
                      <h4 className="font-bold text-white mb-4">Klíčové funkce:</h4>
                      <div className="space-y-2">
                        {service.features.map((feature) => (
                          <div key={feature} className="flex items-center text-sm text-white/90 font-medium">
                            <CheckCircleIcon className="h-4 w-4 text-white mr-2 flex-shrink-0" />
                            <span>{feature}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Benefits section */}
      <div className="py-24 sm:py-32 bg-white">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-5xl font-space mb-8">
                Proč <span className="gradient-text">automatizace</span>?
              </h2>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                {benefits.map((benefit, index) => (
                  <div 
                    key={benefit.title} 
                    className="p-6 rounded-2xl bg-gradient-to-br from-slate-50 to-white border border-slate-200/50 hover:border-purple-200/50 transition-all duration-300"
                    style={{animationDelay: `${index * 0.1}s`}}
                  >
                    <h3 className="text-lg font-semibold text-slate-900 mb-2">{benefit.title}</h3>
                    <p className="text-slate-600 text-sm">{benefit.description}</p>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="relative">
              <div className="p-12 rounded-3xl bg-gradient-to-br from-slate-900 to-slate-800 text-white overflow-hidden">
                <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-full blur-3xl -translate-y-32 translate-x-32"></div>
                
                <div className="relative z-10">
                  <h3 className="text-2xl font-bold mb-8">Integrace s populárními nástroji</h3>
                  
                  <div className="grid grid-cols-4 gap-4">
                    {integrations.map((integration) => (
                      <div key={integration.name} className="text-center p-4 rounded-xl bg-white/5 backdrop-blur-sm hover:bg-white/10 transition-colors">
                        <div className="text-2xl mb-2">{integration.logo}</div>
                        <div className="text-xs text-slate-300">{integration.name}</div>
                      </div>
                    ))}
                  </div>
                  
                  <div className="mt-8 p-6 rounded-2xl bg-white/5 backdrop-blur-sm">
                    <h4 className="font-semibold mb-4">Výsledky našich klientů:</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <div className="text-green-400 font-bold">+150%</div>
                        <div className="text-slate-300">Růst leadů</div>
                      </div>
                      <div>
                        <div className="text-blue-400 font-bold">-60%</div>
                        <div className="text-slate-300">Čas na úkoly</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Process section */}
      <div className="py-24 sm:py-32 bg-slate-50/50">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center mb-20">
            <h2 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-5xl font-space">
              Jak to <span className="gradient-text">implementujeme</span>
            </h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="text-center p-8 rounded-3xl bg-white shadow-lg border border-slate-200/50">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-xl">1</span>
              </div>
              <h3 className="text-lg font-bold text-slate-900 mb-4">Audit</h3>
              <p className="text-slate-600 text-sm">Analyzujeme vaše současné procesy a identifikujeme příležitosti</p>
            </div>
            
            <div className="text-center p-8 rounded-3xl bg-white shadow-lg border border-slate-200/50">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-xl">2</span>
              </div>
              <h3 className="text-lg font-bold text-slate-900 mb-4">Design</h3>
              <p className="text-slate-600 text-sm">Navrhujeme optimální automatizační workflow pro vaše potřeby</p>
            </div>
            
            <div className="text-center p-8 rounded-3xl bg-white shadow-lg border border-slate-200/50">
              <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-xl">3</span>
              </div>
              <h3 className="text-lg font-bold text-slate-900 mb-4">Implementace</h3>
              <p className="text-slate-600 text-sm">Postupně zavádíme systémy s minimálním dopadem na provoz</p>
            </div>
            
            <div className="text-center p-8 rounded-3xl bg-white shadow-lg border border-slate-200/50">
              <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-xl">4</span>
              </div>
              <h3 className="text-lg font-bold text-slate-900 mb-4">Optimalizace</h3>
              <p className="text-slate-600 text-sm">Kontinuálně ladíme a vylepšujeme pro maximální efektivitu</p>
            </div>
          </div>
        </div>
      </div>

      {/* CTA section */}
      <div className="relative py-24 sm:py-32 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-full blur-3xl float-animation"></div>
        </div>
        
        <div className="relative z-10 mx-auto max-w-4xl px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold tracking-tight text-white sm:text-6xl font-space mb-6">
            Připraveni na <span className="gradient-text">automatizaci</span>?
          </h2>
          
          <p className="mx-auto max-w-2xl text-xl leading-8 text-slate-300 mb-12">
            Nechte systémy pracovat za vás a soustřeďte se na růst svého podnikání
          </p>
          
          <Link
            href="/kontakt"
            className="group relative px-10 py-4 text-lg font-semibold bg-white text-slate-900 rounded-2xl hover:bg-slate-100 transition-all duration-300 hover:scale-105 neon-glow shadow-xl"
          >
            <span className="relative z-10 flex items-center gap-2">
              <BoltIcon className="w-5 h-5" />
              Spustit automatizaci
            </span>
          </Link>
        </div>
      </div>
    </div>
  )
}
