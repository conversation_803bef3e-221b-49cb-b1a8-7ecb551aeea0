import Link from 'next/link'
import { 
  UserGroupIcon,
  LightBulbIcon,
  TrophyIcon,
  HeartIcon,
  CheckCircleIcon,
  ArrowRightIcon,
  SparklesIcon,
  RocketLaunchIcon
} from '@heroicons/react/24/outline'

const founders = [
  {
    name: '<PERSON><PERSON><PERSON>',
    role: 'Co-founder & CEO',
    description: 'Expert na digitální strategii s 12letou praxí v marketingu pro MSP',
    expertise: ['Digital Strategy', 'Business Development', 'Team Leadership', 'Client Relations'],
    gradient: 'from-blue-500 to-cyan-500'
  },
  {
    name: '<PERSON>',
    role: 'Co-founder & CTO',
    description: 'Technologický vizionář specializující se na automatizaci a CRM systémy',
    expertise: ['Marketing Automation', 'CRM Systems', 'Data Analytics', 'Tech Innovation'],
    gradient: 'from-purple-500 to-pink-500'
  }
]

const values = [
  {
    icon: HeartIcon,
    title: 'Zákaník na prvním místě',
    description: '<PERSON><PERSON><PERSON>ch je náš úspěch. <PERSON><PERSON><PERSON><PERSON> r<PERSON>ho<PERSON> děláme s ohledem na vaše potřeby.',
    gradient: 'from-red-500 to-pink-500'
  },
  {
    icon: LightBulbIcon,
    title: 'Inovace a kreativita',
    description: 'Neustále hledáme nové způsoby, jak zlepšit výsledky našich klientů.',
    gradient: 'from-yellow-500 to-orange-500'
  },
  {
    icon: TrophyIcon,
    title: 'Excelence v realizaci',
    description: 'Kvalita není náhoda, je výsledkem systematické práce a pozornosti k detailům.',
    gradient: 'from-green-500 to-emerald-500'
  },
  {
    icon: UserGroupIcon,
    title: 'Partnerský přístup',
    description: 'Nejsme jen dodavatel, jsme váš strategický partner pro dlouhodobý růst.',
    gradient: 'from-blue-500 to-indigo-500'
  }
]

const timeline = [
  {
    year: '2018',
    title: 'Založení TK Nurture',
    description: 'Tomáš a Martin spojili síly s vizí pomáhat MSP v digitální transformaci'
  },
  {
    year: '2019',
    title: 'První 50 klientů',
    description: 'Úspěšně jsme pomohli prvním klientům zdvojnásobit jejich online dosah'
  },
  {
    year: '2021',
    title: 'Rozšíření týmu',
    description: 'Přidali jsme specialisty na content marketing a automatizaci'
  },
  {
    year: '2023',
    title: 'AI integrace',
    description: 'Jako jedni z prvních jsme integrovali AI nástroje do našich služeb'
  },
  {
    year: '2024',
    title: '500+ spokojených klientů',
    description: 'Dosáhli jsme milníku 500 úspěšných projektů s průměrným ROI 300%'
  }
]

const stats = [
  { number: '500+', label: 'Spokojených klientů' },
  { number: '6', label: 'Let zkušeností' },
  { number: '300%', label: 'Průměrný růst ROI' },
  { number: '98%', label: 'Spokojenost klientů' }
]

export default function ONas() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
      {/* Hero section */}
      <div className="relative isolate px-6 pt-24 lg:px-8 hero-bg">
        <div className="absolute inset-0 -z-10 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full blur-3xl float-animation"></div>
          <div className="absolute top-3/4 right-1/4 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-cyan-400/20 rounded-full blur-3xl float-animation" style={{animationDelay: '2s'}}></div>
        </div>
        
        <div className="mx-auto max-w-4xl py-32 sm:py-48 lg:py-56 px-4">
          <div className="text-center">
            <div className="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-200/50 mb-8 glass">
              <UserGroupIcon className="w-5 h-5 text-purple-600" />
              <span className="text-sm font-semibold text-purple-800">Náš příběh</span>
            </div>
            
            <h1 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-6xl lg:text-7xl font-space leading-tight">
              O <span className="gradient-text">TK Nurture</span>
            </h1>
            
            <p className="mt-8 text-lg sm:text-xl leading-relaxed text-slate-600 max-w-3xl mx-auto">
              Jsme Tomáš Troup a Martin Koubek - dva marketingoví experti, 
              kteří se rozhodli změnit způsob, jakým malé a střední podniky přistupují k digitálnímu marketingu.
            </p>
          </div>
        </div>
      </div>

      {/* Stats section */}
      <div className="py-24 sm:py-32 bg-slate-50/50">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div 
                key={stat.label}
                className="text-center p-8 rounded-3xl bg-white shadow-lg border border-slate-200/50 card-3d"
                style={{animationDelay: `${index * 0.1}s`}}
              >
                <div className="text-4xl font-bold gradient-text mb-2">{stat.number}</div>
                <div className="text-sm font-medium text-slate-600">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Founders section */}
      <div className="py-24 sm:py-32 bg-white">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center mb-20">
            <h2 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-5xl font-space">
              Zakladatelé <span className="gradient-text">TK Nurture</span>
            </h2>
            <p className="mt-6 text-xl leading-8 text-slate-600">
              Dva experti s jednou vizí - pomáhat podnikům růst efektivně a udržitelně
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {founders.map((founder, index) => (
              <div 
                key={founder.name}
                className="group relative card-3d"
                style={{animationDelay: `${index * 0.2}s`}}
              >
                <div className={`relative h-full p-8 rounded-3xl bg-gradient-to-br ${founder.gradient} shadow-2xl border border-white/30 overflow-hidden`}>
                  <div className="absolute inset-0 opacity-5">
                    <div className="absolute top-0 right-0 w-32 h-32 bg-white rounded-full -translate-y-16 translate-x-16"></div>
                  </div>
                  
                  <div className="relative z-10">
                    <div className="mb-6">
                      <h3 className="text-2xl font-bold text-white mb-2">{founder.name}</h3>
                      <p className="text-lg text-white/90 font-medium">{founder.role}</p>
                    </div>
                    
                    <p className="text-white/95 mb-8 leading-relaxed font-medium">
                      {founder.description}
                    </p>
                    
                    <div>
                      <h4 className="font-bold text-white mb-4">Expertise:</h4>
                      <div className="grid grid-cols-2 gap-2">
                        {founder.expertise.map((skill) => (
                          <div key={skill} className="flex items-center text-sm text-white/90 font-medium">
                            <CheckCircleIcon className="h-4 w-4 text-white mr-2 flex-shrink-0" />
                            <span className="truncate">{skill}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Values section */}
      <div className="py-24 sm:py-32 bg-slate-50/50">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center mb-20">
            <h2 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-5xl font-space">
              Naše <span className="gradient-text">hodnoty</span>
            </h2>
            <p className="mt-6 text-xl leading-8 text-slate-600">
              Principy, které nás vedou v každodenní práci s našimi klienty
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {values.map((value, index) => (
              <div 
                key={value.title}
                className="group relative p-8 rounded-3xl bg-white shadow-lg border border-slate-200/50 hover:border-purple-200/50 transition-all duration-500 card-3d"
                style={{animationDelay: `${index * 0.1}s`}}
              >
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                
                <div className="relative z-10">
                  <div className={`w-12 h-12 bg-gradient-to-br ${value.gradient} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <value.icon className="w-6 h-6 text-white" />
                  </div>
                  
                  <h3 className="text-xl font-semibold text-slate-900 mb-4 group-hover:text-purple-700 transition-colors">
                    {value.title}
                  </h3>
                  
                  <p className="text-slate-600 leading-relaxed">
                    {value.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Timeline section */}
      <div className="py-24 sm:py-32 bg-white">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center mb-20">
            <h2 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-5xl font-space">
              Naše <span className="gradient-text">cesta</span>
            </h2>
            <p className="mt-6 text-xl leading-8 text-slate-600">
              Od malého startupu k přednímu poskytovateli marketingových služeb pro MSP
            </p>
          </div>
          
          <div className="relative">
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-purple-500 to-pink-500 rounded-full"></div>
            
            <div className="space-y-12">
              {timeline.map((item, index) => (
                <div 
                  key={item.year}
                  className={`relative flex items-center ${index % 2 === 0 ? 'justify-start' : 'justify-end'}`}
                  style={{animationDelay: `${index * 0.2}s`}}
                >
                  <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8' : 'pl-8'}`}>
                    <div className="p-6 rounded-2xl bg-gradient-to-br from-slate-50 to-white border border-slate-200/50 shadow-lg">
                      <div className="text-2xl font-bold gradient-text mb-2">{item.year}</div>
                      <h3 className="text-lg font-semibold text-slate-900 mb-2">{item.title}</h3>
                      <p className="text-slate-600">{item.description}</p>
                    </div>
                  </div>
                  
                  <div className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full border-4 border-white shadow-lg"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* CTA section */}
      <div className="relative py-24 sm:py-32 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full blur-3xl float-animation"></div>
        </div>
        
        <div className="relative z-10 mx-auto max-w-4xl px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold tracking-tight text-white sm:text-6xl font-space mb-6">
            Připraveni na <span className="gradient-text">spolupráci</span>?
          </h2>
          
          <p className="mx-auto max-w-2xl text-xl leading-8 text-slate-300 mb-12">
            Pojďme společně transformovat váš podnik a dosáhnout výsledků, o kterých jste snili
          </p>
          
          <div className="flex flex-col sm:flex-row items-center justify-center gap-6">
            <Link
              href="/kontakt"
              className="group relative px-10 py-4 text-lg font-semibold bg-white text-slate-900 rounded-2xl hover:bg-slate-100 transition-all duration-300 hover:scale-105 neon-glow shadow-xl"
            >
              <span className="relative z-10 flex items-center gap-2">
                <SparklesIcon className="w-5 h-5" />
                Začít spolupráci
              </span>
            </Link>
            
            <Link 
              href="/" 
              className="group flex items-center gap-2 px-8 py-4 text-lg font-medium text-white hover:text-purple-300 transition-colors border border-white/20 rounded-2xl hover:border-purple-300"
            >
              Zpět na hlavní stránku
              <ArrowRightIcon className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
