import Link from 'next/link'
import {
  ChartBarIcon,
  MegaphoneIcon,
  ComputerDesktopIcon,
  UserGroupIcon,
  CheckCircleIcon,
  ArrowRightIcon,
  SparklesIcon,
  RocketLaunchIcon,
  BoltIcon
} from '@heroicons/react/24/outline'

const features = [
  {
    name: 'Vedení & Distribuce',
    title: 'Profesionální tým pro váš obsah',
    description: 'Kompletní tým specialistů, kteří se postarají o každý aspekt vašeho obsahu - od natáčení přes právní ochranu až po SEO optimalizaci.',
    icon: MegaphoneIcon,
    href: '/content-distribuce',
    services: ['Kameraman', 'Střihač', 'Grafik', 'Právník', 'SEO Expert'],
    gradient: 'from-purple-500 to-pink-500',
    iconColor: 'text-purple-400'
  },
  {
    name: 'Content Marketing',
    title: 'Dominujte na všech platformách',
    description: 'Strategická tvorba a distribuce obsahu napříč všemi důležitými sociálními sítěmi a vyhledávači pro maximální dosah.',
    icon: ChartBarIcon,
    href: '/tvorba-contentu',
    services: ['Facebook', 'Instagram', 'TikTok', 'YouTube', 'LinkedIn', 'X (Twitter)'],
    gradient: 'from-blue-500 to-cyan-500',
    iconColor: 'text-blue-400'
  },
  {
    name: 'Web & Automatizace',
    title: 'Inteligentní systémy pro růst',
    description: 'Moderní webové řešení propojené s CRM systémy a automatizací, které pracuje za vás 24/7.',
    icon: ComputerDesktopIcon,
    href: '/web-crm',
    services: ['Chatboty', 'Formuláře', 'CRM', 'Databáze', 'Automatické inzeráty'],
    gradient: 'from-green-500 to-emerald-500',
    iconColor: 'text-green-400'
  },
]

const benefits = [
  {
    title: 'Bez starostí, s výsledky',
    description: 'Převezmeme veškerou práci kolem marketingu, vy se soustřeďte na své podnikání'
  },
  {
    title: 'Kompletní pokrytí',
    description: 'Všechny marketingové kanály pod jednou střechou - žádné hledání dalších dodavatelů'
  },
  {
    title: 'Zaměření na MSP',
    description: 'Rozumíme specifickým potřebám malých a středních podniků a jejich rozpočtům'
  },
  {
    title: 'Rychlý start',
    description: 'Během týdnů, ne měsíců, budete mít funkční marketingový systém'
  },
  {
    title: 'Expertní tým',
    description: 'Přístup k týmu specialistů, který by si jinak nemohl dovolit žádný MSP'
  },
  {
    title: 'Měřitelný ROI',
    description: 'Transparentní reporty a jasně měřitelný návrat investice'
  }
]

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
      {/* Hero section */}
      <div className="relative isolate px-6 pt-14 lg:px-8 hero-bg">
        {/* Animated background elements */}
        <div className="absolute inset-0 -z-10 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full blur-3xl float-animation"></div>
          <div className="absolute top-3/4 right-1/4 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-cyan-400/20 rounded-full blur-3xl float-animation" style={{animationDelay: '2s'}}></div>
          <div className="absolute top-1/2 left-1/2 w-64 h-64 bg-gradient-to-r from-green-400/20 to-emerald-400/20 rounded-full blur-3xl float-animation" style={{animationDelay: '4s'}}></div>
        </div>

        <div className="mx-auto max-w-5xl py-32 sm:py-48 lg:py-56 px-4">
          <div className="text-center">
            {/* Badge */}
            <div className="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-200/50 mb-8 glass">
              <SparklesIcon className="w-5 h-5 text-purple-600" />
              <span className="text-sm font-semibold text-purple-800">Troup & Koubek Marketing</span>
            </div>

            <h1 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-6xl lg:text-7xl font-space leading-tight">
              Transformujte svůj podnik na{' '}
              <span className="gradient-text">digitálního lídra</span>
            </h1>

            <p className="mt-8 text-lg sm:text-xl leading-relaxed text-slate-600 max-w-3xl mx-auto">
              Komplexní marketingové řešení pro malé a střední podniky. Převezmeme veškerou práci kolem
              online marketingu, abyste se mohli soustředit na to, co umíte nejlépe - řídit svůj byznys.
            </p>

            {/* Stats */}
            <div className="mt-16 grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-3xl mx-auto">
              <div className="text-center p-6 rounded-2xl bg-white/50 backdrop-blur-sm border border-slate-200/50">
                <div className="text-3xl font-bold gradient-text mb-2">500+</div>
                <div className="text-sm font-medium text-slate-700">Spokojených klientů</div>
              </div>
              <div className="text-center p-6 rounded-2xl bg-white/50 backdrop-blur-sm border border-slate-200/50">
                <div className="text-3xl font-bold gradient-text mb-2">24/7</div>
                <div className="text-sm font-medium text-slate-700">Automatizace</div>
              </div>
              <div className="text-center p-6 rounded-2xl bg-white/50 backdrop-blur-sm border border-slate-200/50">
                <div className="text-3xl font-bold gradient-text mb-2">300%</div>
                <div className="text-sm font-medium text-slate-700">Průměrný růst ROI</div>
              </div>
            </div>

            <div className="mt-16 flex flex-col sm:flex-row items-center justify-center gap-6">
              <Link
                href="/kontakt"
                className="group relative px-10 py-4 text-lg font-semibold text-white rounded-2xl btn-futuristic neon-glow overflow-hidden shadow-xl"
              >
                <RocketLaunchIcon className="w-5 h-5 inline mr-2" />
                Začít transformaci
                <div className="absolute inset-0 shimmer-effect opacity-0 group-hover:opacity-100 transition-opacity"></div>
              </Link>

              <Link
                href="/o-nas"
                className="group flex items-center gap-2 px-8 py-4 text-lg font-medium text-slate-700 hover:text-purple-600 transition-colors border border-slate-300 rounded-2xl hover:border-purple-300"
              >
                Jak to funguje
                <ArrowRightIcon className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Features section */}
      <div className="py-24 sm:py-32 bg-slate-50/50">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-200/50 mb-6">
              <BoltIcon className="w-4 h-4 text-blue-500" />
              <span className="text-sm font-medium text-blue-700">Naše služby</span>
            </div>

            <h2 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-5xl font-space">
              Tři pilíře vašeho <span className="gradient-text">digitálního úspěchu</span>
            </h2>
            <p className="mt-6 text-xl leading-8 text-slate-600">
              Kompletní ekosystém služeb navržený speciálně pro potřeby malých a středních podniků
            </p>
          </div>

          <div className="mx-auto mt-20 max-w-none">
            <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
              {features.map((feature, index) => (
                <div
                  key={feature.name}
                  className="group relative card-3d"
                  style={{animationDelay: `${index * 0.2}s`}}
                >
                  <div className={`relative h-full p-8 rounded-3xl bg-gradient-to-br ${feature.gradient} shadow-2xl border border-white/30 overflow-hidden`}>
                    {/* Background pattern */}
                    <div className="absolute inset-0 opacity-5">
                      <div className="absolute top-0 right-0 w-32 h-32 bg-white rounded-full -translate-y-16 translate-x-16"></div>
                      <div className="absolute bottom-0 left-0 w-24 h-24 bg-white rounded-full translate-y-12 -translate-x-12"></div>
                    </div>

                    {/* Content */}
                    <div className="relative z-10">
                      <div className="flex items-center gap-4 mb-6">
                        <div className={`p-4 rounded-2xl bg-white/25 backdrop-blur-sm shadow-lg`}>
                          <feature.icon className={`h-8 w-8 text-white`} />
                        </div>
                        <div>
                          <h3 className="text-xl font-bold text-white">{feature.name}</h3>
                          <p className="text-sm text-white/90 font-medium">{feature.title}</p>
                        </div>
                      </div>

                      <p className="text-white/95 mb-8 leading-relaxed font-medium">
                        {feature.description}
                      </p>

                      <div className="mb-8">
                        <h4 className="font-bold text-white mb-4">Co zahrnujeme:</h4>
                        <div className="grid grid-cols-2 gap-3">
                          {feature.services.map((service) => (
                            <div key={service} className="flex items-center text-sm text-white/90 font-medium">
                              <CheckCircleIcon className="h-4 w-4 text-white mr-2 flex-shrink-0" />
                              <span className="truncate">{service}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      <Link
                        href={feature.href}
                        className="group/link inline-flex items-center gap-2 px-6 py-3 bg-white/30 hover:bg-white/40 backdrop-blur-sm rounded-xl text-white font-semibold transition-all duration-300 hover:scale-105 shadow-lg"
                      >
                        Zjistit více
                        <ArrowRightIcon className="w-4 h-4 group-hover/link:translate-x-1 transition-transform" />
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Benefits section */}
      <div className="py-24 sm:py-32 bg-white">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center mb-20">
            <h2 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-5xl font-space">
              Proč <span className="gradient-text">TK Nurture</span> funguje?
            </h2>
            <p className="mt-6 text-xl leading-8 text-slate-600">
              Nejsme jen další agentura. Jsme váš strategický partner pro digitální transformaci.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => (
              <div
                key={benefit.title}
                className="group relative p-8 rounded-3xl bg-gradient-to-br from-slate-50 to-white border border-slate-200/50 hover:border-purple-200/50 transition-all duration-500 card-3d"
                style={{animationDelay: `${index * 0.1}s`}}
              >
                {/* Hover effect background */}
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                <div className="relative z-10">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <CheckCircleIcon className="w-6 h-6 text-white" />
                  </div>

                  <h3 className="text-xl font-semibold text-slate-900 mb-3 group-hover:text-purple-700 transition-colors">
                    {benefit.title}
                  </h3>

                  <p className="text-slate-600 leading-relaxed">
                    {benefit.description}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* Team section */}
          <div className="mt-20">
            <div className="relative p-12 rounded-3xl bg-gradient-to-br from-slate-900 to-slate-800 text-white overflow-hidden">
              {/* Background decoration */}
              <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full blur-3xl -translate-y-32 translate-x-32"></div>
              <div className="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-full blur-3xl translate-y-24 -translate-x-24"></div>

              <div className="relative z-10 grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                  <div className="flex items-center gap-4 mb-6">
                    <UserGroupIcon className="h-12 w-12 text-purple-400" />
                    <div>
                      <h3 className="text-2xl font-bold">Troup & Koubek</h3>
                      <p className="text-slate-300">Zakladatelé a vedoucí tým</p>
                    </div>
                  </div>

                  <p className="text-slate-300 text-lg leading-relaxed mb-8">
                    Náš tým zkušených marketingových expertů s více než 10letou praxí v oblasti
                    digitálního marketingu. Specializujeme se výhradně na malé a střední podniky,
                    protože rozumíme jejich specifickým výzvám a omezeným zdrojům.
                  </p>

                  <Link
                    href="/o-nas"
                    className="group inline-flex items-center gap-2 px-6 py-3 bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-xl font-medium transition-all duration-300"
                  >
                    Poznejte náš příběh
                    <ArrowRightIcon className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </div>

                <div className="grid grid-cols-2 gap-6">
                  <div className="text-center p-6 rounded-2xl bg-white/5 backdrop-blur-sm">
                    <div className="text-3xl font-bold text-purple-400 mb-2">10+</div>
                    <div className="text-sm text-slate-300">Let zkušeností</div>
                  </div>
                  <div className="text-center p-6 rounded-2xl bg-white/5 backdrop-blur-sm">
                    <div className="text-3xl font-bold text-blue-400 mb-2">500+</div>
                    <div className="text-sm text-slate-300">Úspěšných projektů</div>
                  </div>
                  <div className="text-center p-6 rounded-2xl bg-white/5 backdrop-blur-sm">
                    <div className="text-3xl font-bold text-green-400 mb-2">98%</div>
                    <div className="text-sm text-slate-300">Spokojenost klientů</div>
                  </div>
                  <div className="text-center p-6 rounded-2xl bg-white/5 backdrop-blur-sm">
                    <div className="text-3xl font-bold text-pink-400 mb-2">24/7</div>
                    <div className="text-sm text-slate-300">Podpora</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* CTA section */}
      <div className="relative py-24 sm:py-32 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
        {/* Background effects */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full blur-3xl float-animation"></div>
          <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-full blur-3xl float-animation" style={{animationDelay: '3s'}}></div>
        </div>

        <div className="relative z-10 mx-auto max-w-4xl px-6 lg:px-8 text-center">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 mb-8">
            <RocketLaunchIcon className="w-4 h-4 text-purple-300" />
            <span className="text-sm font-medium text-purple-200">Připraveni na změnu?</span>
          </div>

          <h2 className="text-4xl font-bold tracking-tight text-white sm:text-6xl font-space mb-6">
            Začněte svou <span className="gradient-text">digitální transformaci</span> ještě dnes
          </h2>

          <p className="mx-auto max-w-2xl text-xl leading-8 text-slate-300 mb-12">
            Získejte bezplatnou konzultaci a strategický plán pro váš podnik.
            Během 30 minut vám ukážeme, jak můžete zdvojnásobit svůj online dosah.
          </p>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-6">
            <Link
              href="/kontakt"
              className="group relative px-8 py-4 text-lg font-semibold bg-white text-slate-900 rounded-2xl hover:bg-slate-100 transition-all duration-300 hover:scale-105 neon-glow"
            >
              <span className="relative z-10 flex items-center gap-2">
                <SparklesIcon className="w-5 h-5" />
                Bezplatná konzultace
              </span>
            </Link>

            <Link
              href="/tvorba-contentu"
              className="group flex items-center gap-2 px-6 py-3 text-lg font-medium text-white hover:text-purple-300 transition-colors"
            >
              Prohlédnout služby
              <ArrowRightIcon className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
            </Link>
          </div>

          {/* Trust indicators */}
          <div className="mt-16 grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-2xl mx-auto">
            <div className="text-center">
              <div className="text-2xl font-bold text-white mb-2">✓ Bez závazků</div>
              <div className="text-sm text-slate-400">30min konzultace zdarma</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-white mb-2">✓ Rychlý start</div>
              <div className="text-sm text-slate-400">Výsledky během týdnů</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-white mb-2">✓ Garance ROI</div>
              <div className="text-sm text-slate-400">Nebo peníze zpět</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
