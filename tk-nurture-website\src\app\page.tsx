import Link from 'next/link'
import {
  CameraIcon,
  CogIcon,
  RocketLaunchIcon,
  CheckCircleIcon,
  ArrowRightIcon,
  SparklesIcon,
  BoltIcon,
  ChartBarIcon,
  PlayIcon
} from '@heroicons/react/24/outline'

const services = [
  {
    name: 'Produkce obsahu na sociální sítě',
    title: 'Kompletní content produkce',
    description: 'Kompletní řešení pro tvorbu a distribuci obsahu napříč všemi sociálními sítěmi. Od natáčení po publikování - vše pod jednou střechou.',
    icon: CameraIcon,
    services: ['<PERSON><PERSON>aman', 'Střihač', 'Grafik', 'Copywriter', 'Social Media Manager'],
    gradient: 'from-slate-800 via-gray-700 to-zinc-800',
    iconColor: 'text-slate-300'
  },
  {
    name: 'CRM a Automatizace',
    title: 'Inteligentní systémy pro růst',
    description: 'Automatizujte marketing, zrychlete růst. Využijte sílu automatizace k odemknutí skryté hodnoty ve vašich procesech.',
    icon: CogIcon,
    services: ['Lead Management', 'Email Automation', 'Sales Pipeline', 'Customer Support', 'Analytics'],
    gradient: 'from-gray-800 via-slate-700 to-gray-800',
    iconColor: 'text-gray-300'
  },
  {
    name: 'Náš proces implementace',
    title: 'Systematický přístup k úspěchu',
    description: 'Detailní roadmapa implementace, která vás provede od analýzy až po spuštění kompletního marketingového ekosystému.',
    icon: RocketLaunchIcon,
    services: ['Strategická analýza', 'Technické založení', 'Tým specialistů', 'Spuštění a optimalizace'],
    gradient: 'from-zinc-800 via-slate-700 to-zinc-800',
    iconColor: 'text-zinc-300'
  }
]

const benefits = [
  {
    title: 'Bez starostí, s výsledky',
    description: 'Převezmeme veškerou práci kolem marketingu, vy se soustřeďte na své podnikání',
    icon: '🎯'
  },
  {
    title: 'Kompletní pokrytí',
    description: 'Všechny marketingové kanály pod jednou střechou - žádné hledání dalších dodavatelů',
    icon: '🔧'
  },
  {
    title: 'Zaměření na MSP',
    description: 'Rozumíme specifickým potřebám malých a středních podniků a jejich rozpočtům',
    icon: '🏢'
  },
  {
    title: 'Rychlý start',
    description: 'Během týdnů, ne měsíců, budete mít funkční marketingový systém',
    icon: '⚡'
  },
  {
    title: 'Expertní tým',
    description: 'Přístup k týmu specialistů, který by si jinak nemohl dovolit žádný MSP',
    icon: '👥'
  },
  {
    title: 'Měřitelný ROI',
    description: 'Transparentní reporty a jasně měřitelný návrat investice',
    icon: '📊'
  }
]



export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-slate-900">
      {/* Hero section */}
      <div className="relative isolate px-6 pt-14 lg:px-8">
        {/* Animated background elements */}
        <div className="absolute inset-0 -z-10 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-slate-600/20 to-gray-500/20 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute top-3/4 right-1/4 w-80 h-80 bg-gradient-to-r from-zinc-600/20 to-slate-500/20 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>
          <div className="absolute top-1/2 left-1/2 w-64 h-64 bg-gradient-to-r from-gray-600/20 to-zinc-500/20 rounded-full blur-3xl animate-pulse" style={{animationDelay: '4s'}}></div>

          {/* Metallic grid pattern */}
          <div className="absolute inset-0 bg-[linear-gradient(rgba(148,163,184,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(148,163,184,0.03)_1px,transparent_1px)] bg-[size:50px_50px]"></div>
        </div>

        <div className="mx-auto max-w-5xl py-16 sm:py-24 lg:py-32 px-4">
          <div className="text-center">
            {/* Badge */}
            <div className="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-gradient-to-r from-slate-800/80 to-gray-800/80 border border-slate-600/50 mb-8 backdrop-blur-sm">
              <SparklesIcon className="w-5 h-5 text-slate-300" />
              <span className="text-sm font-semibold text-slate-200">Troup & Koubek Marketing</span>
            </div>

            <h1 className="text-4xl font-bold tracking-tight text-white sm:text-6xl lg:text-7xl font-space leading-tight">
              Transformujte svůj podnik na{' '}
              <span className="bg-gradient-to-r from-slate-300 via-gray-200 to-slate-300 bg-clip-text text-transparent">digitálního lídra</span>
            </h1>

            <p className="mt-8 text-lg sm:text-xl leading-relaxed text-slate-300 max-w-3xl mx-auto">
              Komplexní marketingové řešení pro malé a střední podniky. Převezmeme veškerou práci kolem
              online marketingu, abyste se mohli soustředit na to, co umíte nejlépe - řídit svůj byznys.
            </p>

            {/* Stats */}
            <div className="mt-16 grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-3xl mx-auto">
              <div className="text-center p-6 rounded-2xl bg-gradient-to-br from-slate-800/50 to-gray-800/50 backdrop-blur-sm border border-slate-600/30">
                <div className="text-3xl font-bold bg-gradient-to-r from-slate-200 to-gray-300 bg-clip-text text-transparent mb-2">500+</div>
                <div className="text-sm font-medium text-slate-400">Spokojených klientů</div>
              </div>
              <div className="text-center p-6 rounded-2xl bg-gradient-to-br from-slate-800/50 to-gray-800/50 backdrop-blur-sm border border-slate-600/30">
                <div className="text-3xl font-bold bg-gradient-to-r from-slate-200 to-gray-300 bg-clip-text text-transparent mb-2">24/7</div>
                <div className="text-sm font-medium text-slate-400">Automatizace</div>
              </div>
              <div className="text-center p-6 rounded-2xl bg-gradient-to-br from-slate-800/50 to-gray-800/50 backdrop-blur-sm border border-slate-600/30">
                <div className="text-3xl font-bold bg-gradient-to-r from-slate-200 to-gray-300 bg-clip-text text-transparent mb-2">300%</div>
                <div className="text-sm font-medium text-slate-400">Průměrný růst ROI</div>
              </div>
            </div>

            <div className="mt-16 flex flex-col sm:flex-row items-center justify-center gap-6">
              <button
                data-cal-link="tknurture/hovor-s-tknurture"
                data-cal-namespace="hovor-s-tknurture"
                data-cal-config='{"layout":"month_view"}'
                className="group relative px-10 py-4 text-lg font-semibold text-black rounded-2xl bg-gradient-to-r from-slate-200 via-gray-100 to-slate-200 hover:from-slate-300 hover:via-gray-200 hover:to-slate-300 transition-all duration-300 shadow-xl hover:shadow-2xl hover:scale-105"
              >
                <RocketLaunchIcon className="w-5 h-5 inline mr-2" />
                Začít transformaci
              </button>

              <Link
                href="#sluzby"
                className="group flex items-center gap-2 px-8 py-4 text-lg font-medium text-slate-300 hover:text-white transition-colors border border-slate-600 rounded-2xl hover:border-slate-400"
              >
                Jak to funguje
                <ArrowRightIcon className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Services section */}
      <div id="sluzby" className="py-24 sm:py-32 bg-gradient-to-br from-gray-900 via-slate-800 to-gray-900">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-slate-700/50 to-gray-700/50 border border-slate-600/50 mb-6 backdrop-blur-sm">
              <BoltIcon className="w-4 h-4 text-slate-300" />
              <span className="text-sm font-medium text-slate-200">Naše služby</span>
            </div>

            <h2 className="text-4xl font-bold tracking-tight text-white sm:text-5xl font-space">
              Tři pilíře vašeho <span className="bg-gradient-to-r from-slate-300 via-gray-200 to-slate-300 bg-clip-text text-transparent">digitálního úspěchu</span>
            </h2>
            <p className="mt-6 text-xl leading-8 text-slate-300">
              Kompletní ekosystém služeb navržený speciálně pro potřeby malých a středních podniků
            </p>
          </div>

          <div className="mx-auto mt-20 max-w-none">
            <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
              {services.map((service, index) => (
                <div
                  key={service.name}
                  className="group relative"
                  style={{animationDelay: `${index * 0.2}s`}}
                >
                  <div className={`relative h-full p-8 rounded-3xl bg-gradient-to-br ${service.gradient} shadow-2xl border border-slate-600/30 overflow-hidden backdrop-blur-sm hover:border-slate-500/50 transition-all duration-500`}>
                    {/* Metallic shine effect */}
                    <div className="absolute inset-0 bg-gradient-to-br from-slate-400/10 via-transparent to-gray-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    {/* Grid pattern overlay */}
                    <div className="absolute inset-0 bg-[linear-gradient(rgba(148,163,184,0.05)_1px,transparent_1px),linear-gradient(90deg,rgba(148,163,184,0.05)_1px,transparent_1px)] bg-[size:20px_20px]"></div>

                    {/* Content */}
                    <div className="relative z-10">
                      <div className="flex items-center gap-4 mb-6">
                        <div className="p-4 rounded-2xl bg-slate-700/50 backdrop-blur-sm shadow-lg border border-slate-600/30">
                          <service.icon className="h-8 w-8 text-slate-200" />
                        </div>
                        <div>
                          <h3 className="text-xl font-bold text-white">{service.name}</h3>
                          <p className="text-sm text-slate-300 font-medium">{service.title}</p>
                        </div>
                      </div>

                      <p className="text-slate-200 mb-8 leading-relaxed font-medium">
                        {service.description}
                      </p>

                      <div className="mb-8">
                        <h4 className="font-bold text-white mb-4">Co zahrnujeme:</h4>
                        <div className="grid grid-cols-1 gap-3">
                          {service.services.map((item, serviceIndex) => (
                            <div key={serviceIndex} className="flex items-center text-sm text-slate-300 font-medium">
                              <CheckCircleIcon className="h-4 w-4 text-slate-400 mr-2 flex-shrink-0" />
                              <span>{item}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="inline-flex items-center gap-2 px-6 py-3 bg-slate-600/30 hover:bg-slate-600/50 backdrop-blur-sm rounded-xl text-slate-200 font-semibold transition-all duration-300 hover:scale-105 shadow-lg border border-slate-500/30">
                        Zjistit více
                        <ArrowRightIcon className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Benefits section */}
      <div className="py-24 sm:py-32 bg-gradient-to-br from-slate-900 via-gray-800 to-slate-900">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center mb-20">
            <h2 className="text-4xl font-bold tracking-tight text-white sm:text-5xl font-space">
              Proč <span className="bg-gradient-to-r from-slate-300 via-gray-200 to-slate-300 bg-clip-text text-transparent">TK Nurture</span> funguje?
            </h2>
            <p className="mt-6 text-xl leading-8 text-slate-300">
              Nejsme jen další agentura. Jsme váš strategický partner pro digitální transformaci.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => (
              <div
                key={benefit.title}
                className="group relative p-8 rounded-3xl bg-gradient-to-br from-slate-800/50 to-gray-800/50 border border-slate-600/30 hover:border-slate-500/50 transition-all duration-500 backdrop-blur-sm"
                style={{animationDelay: `${index * 0.1}s`}}
              >
                {/* Metallic shine effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-slate-400/10 via-transparent to-gray-400/10 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                <div className="relative z-10">
                  <div className="w-16 h-16 bg-gradient-to-br from-slate-700 to-gray-800 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 border border-slate-600/50">
                    <span className="text-2xl">{benefit.icon}</span>
                  </div>

                  <h3 className="text-xl font-semibold text-white mb-3 group-hover:text-slate-200 transition-colors">
                    {benefit.title}
                  </h3>

                  <p className="text-slate-300 leading-relaxed">
                    {benefit.description}
                  </p>
                </div>
              </div>
            ))}
          </div>


        </div>
      </div>

      {/* Contact section */}
      <div id="kontakt" className="relative py-24 sm:py-32 bg-gradient-to-br from-black via-gray-900 to-slate-900 overflow-hidden">
        {/* Background effects */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-slate-600/20 to-gray-500/20 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-zinc-600/20 to-slate-500/20 rounded-full blur-3xl animate-pulse" style={{animationDelay: '3s'}}></div>

          {/* Metallic grid pattern */}
          <div className="absolute inset-0 bg-[linear-gradient(rgba(148,163,184,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(148,163,184,0.03)_1px,transparent_1px)] bg-[size:50px_50px]"></div>
        </div>

        <div className="relative z-10 mx-auto max-w-4xl px-6 lg:px-8 text-center">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-slate-800/80 backdrop-blur-sm border border-slate-600/50 mb-8">
            <RocketLaunchIcon className="w-4 h-4 text-slate-300" />
            <span className="text-sm font-medium text-slate-200">Připraveni na změnu?</span>
          </div>

          <h2 className="text-4xl font-bold tracking-tight text-white sm:text-6xl font-space mb-6">
            Začněte svou <span className="bg-gradient-to-r from-slate-300 via-gray-200 to-slate-300 bg-clip-text text-transparent">digitální transformaci</span> ještě dnes
          </h2>

          <p className="mx-auto max-w-2xl text-xl leading-8 text-slate-300 mb-12">
            Získejte bezplatnou konzultaci a strategický plán pro váš podnik.
            Během 30 minut vám ukážeme, jak můžete zdvojnásobit svůj online dosah.
          </p>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-6">
            <button
              data-cal-link="tknurture/hovor-s-tknurture"
              data-cal-namespace="hovor-s-tknurture"
              data-cal-config='{"layout":"month_view"}'
              className="group relative px-8 py-4 text-lg font-semibold bg-gradient-to-r from-slate-200 via-gray-100 to-slate-200 text-black rounded-2xl hover:from-slate-300 hover:via-gray-200 hover:to-slate-300 transition-all duration-300 hover:scale-105 shadow-xl"
            >
              <span className="relative z-10 flex items-center gap-2">
                <SparklesIcon className="w-5 h-5" />
                Bezplatná konzultace
              </span>
            </button>

            <Link
              href="#sluzby"
              className="group flex items-center gap-2 px-6 py-3 text-lg font-medium text-slate-300 hover:text-white transition-colors border border-slate-600 rounded-2xl hover:border-slate-400"
            >
              Prohlédnout služby
              <ArrowRightIcon className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
            </Link>
          </div>

          {/* Trust indicators */}
          <div className="mt-16 grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-2xl mx-auto">
            <div className="text-center p-6 rounded-2xl bg-slate-800/50 backdrop-blur-sm border border-slate-600/30">
              <div className="text-2xl font-bold text-white mb-2">✓ Bez závazků</div>
              <div className="text-sm text-slate-400">30min konzultace zdarma</div>
            </div>
            <div className="text-center p-6 rounded-2xl bg-slate-800/50 backdrop-blur-sm border border-slate-600/30">
              <div className="text-2xl font-bold text-white mb-2">✓ Rychlý start</div>
              <div className="text-sm text-slate-400">Výsledky během týdnů</div>
            </div>
            <div className="text-center p-6 rounded-2xl bg-slate-800/50 backdrop-blur-sm border border-slate-600/30">
              <div className="text-2xl font-bold text-white mb-2">✓ Garance ROI</div>
              <div className="text-sm text-slate-400">Nebo peníze zpět</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
