import Link from 'next/link'

export default function NasProces() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
      <section className="relative pt-20 pb-32 flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900"></div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="animate-fade-in-up">
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6">
              <PERSON><PERSON><PERSON> proces
              <span className="block bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                implementace
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-slate-300 mb-12 max-w-4xl mx-auto leading-relaxed">
              Detailní roadmapa implementace, kter<PERSON> vás provede od analýzy až po spuštění 
              kompletního marketingového ekosystému s týmem specialistů.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/kontakt" className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:from-purple-700 hover:to-pink-700 transition-all duration-300 shadow-lg hover:shadow-purple-500/25 hover:scale-105">
                Začít implementaci
              </Link>
              <button className="border-2 border-purple-500 text-purple-300 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-purple-500/10 transition-all duration-300">
                Prohlédnout proces
              </button>
            </div>
          </div>
        </div>
      </section>

      <section className="py-24 sm:py-32 bg-white">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center mb-20">
            <h2 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-5xl font-space">
              4 kroky k <span className="gradient-text">úspěchu</span>
            </h2>
            <p className="mt-6 text-xl leading-8 text-slate-600">
              Systematický přístup k implementaci, který garantuje výsledky
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center p-8 rounded-3xl bg-gradient-to-br from-slate-50 to-white border border-slate-200/50 hover:border-purple-200/50 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-xl">1</span>
              </div>
              <h3 className="text-xl font-semibold text-slate-900 mb-3">Strategická analýza</h3>
              <p className="text-slate-600">Definice cílů a kanálů podle vašeho byznysu</p>
            </div>
            
            <div className="text-center p-8 rounded-3xl bg-gradient-to-br from-slate-50 to-white border border-slate-200/50 hover:border-purple-200/50 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-xl">2</span>
              </div>
              <h3 className="text-xl font-semibold text-slate-900 mb-3">Technické založení</h3>
              <p className="text-slate-600">Založení účtů a nastavení všech platforem</p>
            </div>
            
            <div className="text-center p-8 rounded-3xl bg-gradient-to-br from-slate-50 to-white border border-slate-200/50 hover:border-purple-200/50 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-xl">3</span>
              </div>
              <h3 className="text-xl font-semibold text-slate-900 mb-3">Tým specialistů</h3>
              <p className="text-slate-600">Přiřazení freelancerů podle vašich potřeb</p>
            </div>
            
            <div className="text-center p-8 rounded-3xl bg-gradient-to-br from-slate-50 to-white border border-slate-200/50 hover:border-purple-200/50 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-xl">4</span>
              </div>
              <h3 className="text-xl font-semibold text-slate-900 mb-3">Spuštění a optimalizace</h3>
              <p className="text-slate-600">Go-live a kontinuální monitoring</p>
            </div>
          </div>
        </div>
      </section>

      <section className="py-24 sm:py-32 bg-slate-50/50">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center mb-20">
            <h2 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-5xl font-space">
              Cenové <span className="gradient-text">balíčky</span> týmu
            </h2>
            <p className="mt-6 text-xl leading-8 text-slate-600">
              Flexibilní řešení podle velikosti a potřeb vašeho podniku
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="group relative p-8 rounded-3xl bg-white shadow-lg border border-slate-200/50 hover:border-purple-200/50 transition-all duration-500">
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-slate-900 mb-2">Starter</h3>
                <p className="text-slate-600 mb-6">Pro začínající podniky</p>
                <div className="flex items-baseline justify-center gap-2">
                  <span className="text-4xl font-bold gradient-text">15 000</span>
                  <span className="text-slate-600">Kč/měsíc</span>
                </div>
              </div>
              <Link href="/kontakt" className="w-full px-6 py-3 text-center font-semibold rounded-2xl transition-all duration-300 block bg-slate-100 text-slate-700 hover:bg-slate-200">
                Vybrat balíček
              </Link>
            </div>

            <div className="group relative p-8 rounded-3xl bg-white shadow-lg border border-purple-200 ring-2 ring-purple-500/20 scale-105 transition-all duration-500">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <div className="px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-sm font-semibold rounded-full">
                  Nejpopulárnější
                </div>
              </div>
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-slate-900 mb-2">Professional</h3>
                <p className="text-slate-600 mb-6">Pro rostoucí firmy</p>
                <div className="flex items-baseline justify-center gap-2">
                  <span className="text-4xl font-bold gradient-text">35 000</span>
                  <span className="text-slate-600">Kč/měsíc</span>
                </div>
              </div>
              <Link href="/kontakt" className="w-full px-6 py-3 text-center font-semibold rounded-2xl transition-all duration-300 block bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:scale-105 neon-glow">
                Vybrat balíček
              </Link>
            </div>

            <div className="group relative p-8 rounded-3xl bg-white shadow-lg border border-slate-200/50 hover:border-purple-200/50 transition-all duration-500">
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-slate-900 mb-2">Enterprise</h3>
                <p className="text-slate-600 mb-6">Pro etablované společnosti</p>
                <div className="flex items-baseline justify-center gap-2">
                  <span className="text-4xl font-bold gradient-text">75 000</span>
                  <span className="text-slate-600">Kč/měsíc</span>
                </div>
              </div>
              <Link href="/kontakt" className="w-full px-6 py-3 text-center font-semibold rounded-2xl transition-all duration-300 block bg-slate-100 text-slate-700 hover:bg-slate-200">
                Vybrat balíček
              </Link>
            </div>
          </div>
        </div>
      </section>

      <section className="relative py-24 sm:py-32 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
        <div className="relative z-10 mx-auto max-w-4xl px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold tracking-tight text-white sm:text-6xl font-space mb-6">
            Začněte svou <span className="gradient-text">transformaci</span> dnes
          </h2>

          <p className="mx-auto max-w-2xl text-xl leading-8 text-slate-300 mb-12">
            Nečekejte na konkurenci. Získejte náskok s kompletním marketingovým ekosystémem.
          </p>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-6">
            <Link href="/kontakt" className="group relative px-10 py-4 text-lg font-semibold bg-white text-slate-900 rounded-2xl hover:bg-slate-100 transition-all duration-300 hover:scale-105 neon-glow shadow-xl">
              Začít implementaci
            </Link>
            <Link href="/" className="group flex items-center gap-2 px-8 py-4 text-lg font-medium text-white hover:text-purple-300 transition-colors border border-white/20 rounded-2xl hover:border-purple-300">
              Zpět na hlavní stránku
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
