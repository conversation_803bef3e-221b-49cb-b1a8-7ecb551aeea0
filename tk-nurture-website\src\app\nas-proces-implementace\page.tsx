'use client'

import { useState } from 'react'
import Link from 'next/link'
import { 
  CheckCircleIcon,
  ArrowRightIcon,
  SparklesIcon,
  RocketLaunchIcon,
  CogIcon,
  UserGroupIcon,
  ChartBarIcon,
  GlobeAltIcon,
  MagnifyingGlassIcon,
  ShareIcon,
  CurrencyDollarIcon,
  ClockIcon
} from '@heroicons/react/24/outline'

const implementationSteps = [
  {
    id: 1,
    title: 'Strategická analýza',
    subtitle: 'Definice cílů a kanálů',
    description: 'Analyzujeme vaše podnikání, cílové skupiny a definujeme optimální distribuční kanály a automatizace.',
    icon: ChartBarIcon,
    duration: '1-2 týdny',
    deliverables: [
      'Audit současného stavu',
      'Analýza konkurence',
      'Definice cílových skupin',
      'Strategie distribučních kanálů',
      'Plán automatizací'
    ],
    gradient: 'from-blue-500 to-cyan-500',
    bgColor: 'bg-blue-50'
  },
  {
    id: 2,
    title: '<PERSON><PERSON><PERSON>',
    subtitle: 'Účty a platformy',
    description: 'Založíme a nastavíme všechny potřebné účty na sociálních sítích, webové nástroje a CRM systémy.',
    icon: GlobeAltIcon,
    duration: '1 týden',
    deliverables: [
      'Sociální média účty',
      'Google Analytics & Search Console',
      'CRM systém',
      'Email marketing nástroje',
      'Webové formuláře'
    ],
    gradient: 'from-purple-500 to-pink-500',
    bgColor: 'bg-purple-50'
  },
  {
    id: 3,
    title: 'Tým specialistů',
    subtitle: 'Přiřazení freelancerů',
    description: 'Podle vašich potřeb a rozpočtu sestavíme tým freelancerů s různými balíčky služeb.',
    icon: UserGroupIcon,
    duration: '3-5 dní',
    deliverables: [
      'Výběr vhodných specialistů',
      'Cenové balíčky',
      'Komunikační kanály',
      'Harmonogram práce',
      'Quality assurance'
    ],
    gradient: 'from-green-500 to-emerald-500',
    bgColor: 'bg-green-50'
  },
  {
    id: 4,
    title: 'Spuštění a optimalizace',
    subtitle: 'Go-live a monitoring',
    description: 'Spustíme všechny systémy, zahájíme content produkci a kontinuálně optimalizujeme výsledky.',
    icon: RocketLaunchIcon,
    duration: 'Ongoing',
    deliverables: [
      'Spuštění kampaní',
      'Content kalendář',
      'Monitoring výsledků',
      'Měsíční reporty',
      'Kontinuální optimalizace'
    ],
    gradient: 'from-orange-500 to-red-500',
    bgColor: 'bg-orange-50'
  }
]

const pricingTiers = [
  {
    name: 'Starter',
    price: '15 000',
    period: 'Kč/měsíc',
    description: 'Pro začínající podniky',
    features: [
      'Grafik (20h/měsíc)',
      'Copywriter (15h/měsíc)',
      'Social media management',
      'Základní SEO',
      'Měsíční report'
    ],
    gradient: 'from-blue-500 to-cyan-500',
    popular: false
  },
  {
    name: 'Professional',
    price: '35 000',
    period: 'Kč/měsíc',
    description: 'Pro rostoucí firmy',
    features: [
      'Grafik (40h/měsíc)',
      'Copywriter (30h/měsíc)',
      'Video editor (20h/měsíc)',
      'SEO specialist (15h/měsíc)',
      'PPC kampaně',
      'Týdenní reporty'
    ],
    gradient: 'from-purple-500 to-pink-500',
    popular: true
  },
  {
    name: 'Enterprise',
    price: '75 000',
    period: 'Kč/měsíc',
    description: 'Pro etablované společnosti',
    features: [
      'Kompletní tým (120h/měsíc)',
      'Kameraman + střihač',
      'Právní poradenství',
      'Pokročilé automatizace',
      'Dedikovaný account manager',
      'Denní monitoring'
    ],
    gradient: 'from-orange-500 to-red-500',
    popular: false
  }
]

export default function NasProces() {
  const [activeStep, setActiveStep] = useState(1)

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
      {/* Hero Section */}
      <section className="relative pt-20 pb-32 flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900"></div>
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%239C92AC" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="animate-fade-in-up">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-purple-500/20 border border-purple-500/30 mb-8">
              <RocketLaunchIcon className="w-4 h-4 text-purple-300 mr-2" />
              <span className="text-purple-300 text-sm font-medium">Implementační proces</span>
            </div>
            
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6">
              Náš proces
              <span className="block bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                implementace
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-slate-300 mb-12 max-w-4xl mx-auto leading-relaxed">
              Detailní roadmapa implementace, která vás provede od analýzy až po spuštění 
              kompletního marketingového ekosystému s týmem specialistů.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/kontakt" className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:from-purple-700 hover:to-pink-700 transition-all duration-300 shadow-lg hover:shadow-purple-500/25 hover:scale-105">
                Začít implementaci
              </Link>
              <button className="border-2 border-purple-500 text-purple-300 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-purple-500/10 transition-all duration-300">
                Prohlédnout proces
              </button>
            </div>
          </div>
        </div>
        
        {/* Floating elements */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-purple-500/20 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-32 h-32 bg-pink-500/20 rounded-full blur-xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-blue-500/20 rounded-full blur-xl animate-pulse delay-500"></div>
      </section>

      {/* Implementation Steps */}
      <section className="py-24 sm:py-32 bg-white">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center mb-20">
            <h2 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-5xl font-space">
              4 kroky k <span className="gradient-text">úspěchu</span>
            </h2>
            <p className="mt-6 text-xl leading-8 text-slate-600">
              Systematický přístup k implementaci, který garantuje výsledky
            </p>
          </div>

          {/* Interactive Timeline */}
          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-blue-500 via-purple-500 via-green-500 to-orange-500 rounded-full"></div>
            
            <div className="space-y-24">
              {implementationSteps.map((step, index) => (
                <div 
                  key={step.id}
                  className={`relative flex items-center ${index % 2 === 0 ? 'justify-start' : 'justify-end'}`}
                  style={{animationDelay: `${index * 0.2}s`}}
                >
                  <div className={`w-1/2 ${index % 2 === 0 ? 'pr-12' : 'pl-12'}`}>
                    <div 
                      className={`group relative p-8 rounded-3xl ${step.bgColor} border border-slate-200/50 hover:border-purple-200/50 transition-all duration-500 cursor-pointer card-3d`}
                      onClick={() => setActiveStep(step.id)}
                    >
                      <div className={`absolute inset-0 bg-gradient-to-br ${step.gradient} opacity-0 group-hover:opacity-5 rounded-3xl transition-opacity duration-500`}></div>
                      
                      <div className="relative z-10">
                        <div className="flex items-center gap-4 mb-6">
                          <div className={`w-12 h-12 bg-gradient-to-br ${step.gradient} rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                            <step.icon className="w-6 h-6 text-white" />
                          </div>
                          <div>
                            <h3 className="text-xl font-bold text-slate-900 group-hover:text-purple-700 transition-colors">
                              {step.title}
                            </h3>
                            <p className="text-sm font-medium text-slate-600">{step.subtitle}</p>
                          </div>
                          <div className="ml-auto flex items-center gap-2 text-sm font-medium text-slate-500">
                            <ClockIcon className="w-4 h-4" />
                            {step.duration}
                          </div>
                        </div>
                        
                        <p className="text-slate-700 leading-relaxed mb-6">
                          {step.description}
                        </p>
                        
                        {activeStep === step.id && (
                          <div className="space-y-3 animate-fadeIn">
                            <h4 className="font-semibold text-slate-900 mb-3">Výstupy:</h4>
                            {step.deliverables.map((deliverable) => (
                              <div key={deliverable} className="flex items-center text-sm text-slate-600">
                                <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                                <span>{deliverable}</span>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {/* Timeline dot */}
                  <div className={`absolute left-1/2 transform -translate-x-1/2 w-6 h-6 bg-gradient-to-br ${step.gradient} rounded-full border-4 border-white shadow-lg z-10`}>
                    <div className="w-full h-full rounded-full bg-white/20"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-24 sm:py-32 bg-slate-50/50">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center mb-20">
            <h2 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-5xl font-space">
              Cenové <span className="gradient-text">balíčky</span> týmu
            </h2>
            <p className="mt-6 text-xl leading-8 text-slate-600">
              Flexibilní řešení podle velikosti a potřeb vašeho podniku
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {pricingTiers.map((tier, index) => (
              <div
                key={tier.name}
                className={`group relative p-8 rounded-3xl bg-white shadow-lg border transition-all duration-500 card-3d ${
                  tier.popular
                    ? 'border-purple-200 ring-2 ring-purple-500/20 scale-105'
                    : 'border-slate-200/50 hover:border-purple-200/50'
                }`}
                style={{animationDelay: `${index * 0.1}s`}}
              >
                {tier.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <div className="px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-sm font-semibold rounded-full">
                      Nejpopulárnější
                    </div>
                  </div>
                )}

                <div className={`absolute inset-0 bg-gradient-to-br ${tier.gradient} opacity-0 group-hover:opacity-5 rounded-3xl transition-opacity duration-500`}></div>

                <div className="relative z-10">
                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold text-slate-900 mb-2">{tier.name}</h3>
                    <p className="text-slate-600 mb-6">{tier.description}</p>
                    <div className="flex items-baseline justify-center gap-2">
                      <span className="text-4xl font-bold gradient-text">{tier.price}</span>
                      <span className="text-slate-600">{tier.period}</span>
                    </div>
                  </div>

                  <div className="space-y-4 mb-8">
                    {tier.features.map((feature) => (
                      <div key={feature} className="flex items-center text-slate-700">
                        <CheckCircleIcon className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <Link
                    href="/kontakt"
                    className={`w-full group relative px-6 py-3 text-center font-semibold rounded-2xl transition-all duration-300 block ${
                      tier.popular
                        ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:scale-105 neon-glow'
                        : 'bg-slate-100 text-slate-700 hover:bg-slate-200'
                    }`}
                  >
                    Vybrat balíček
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Visualization */}
      <section className="py-24 sm:py-32 bg-white">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center mb-20">
            <h2 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-5xl font-space">
              Vizualizace <span className="gradient-text">procesu</span>
            </h2>
            <p className="mt-6 text-xl leading-8 text-slate-600">
              Jak vypadá kompletní implementace v praxi
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <h3 className="text-2xl font-bold text-slate-900 mb-8">
                Od strategie k <span className="gradient-text">realizaci</span>
              </h3>

              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                    1
                  </div>
                  <div>
                    <h4 className="font-semibold text-slate-900 mb-2">Strategická analýza</h4>
                    <p className="text-slate-600 text-sm">Definujeme distribuční cesty, kanály a automatizace podle vašeho byznysu</p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                    2
                  </div>
                  <div>
                    <h4 className="font-semibold text-slate-900 mb-2">Technické nastavení</h4>
                    <p className="text-slate-600 text-sm">Založíme účty na všech platformách podle best practices</p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                    3
                  </div>
                  <div>
                    <h4 className="font-semibold text-slate-900 mb-2">Sestavení týmu</h4>
                    <p className="text-slate-600 text-sm">Přiřadíme freelancery podle vašich potřeb a rozpočtu</p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                    4
                  </div>
                  <div>
                    <h4 className="font-semibold text-slate-900 mb-2">Spuštění a optimalizace</h4>
                    <p className="text-slate-600 text-sm">Zahájíme produkci a kontinuálně optimalizujeme výsledky</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="p-8 rounded-3xl bg-gradient-to-br from-slate-900 to-slate-800 text-white overflow-hidden">
                <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full blur-3xl -translate-y-32 translate-x-32"></div>

                <div className="relative z-10">
                  <h3 className="text-xl font-bold mb-6">Garantované výsledky</h3>

                  <div className="grid grid-cols-2 gap-6 mb-8">
                    <div className="text-center">
                      <div className="text-3xl font-bold gradient-text mb-2">300%</div>
                      <div className="text-sm text-slate-300">Průměrný růst dosahu</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold gradient-text mb-2">90</div>
                      <div className="text-sm text-slate-300">Dní do prvních výsledků</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold gradient-text mb-2">24/7</div>
                      <div className="text-sm text-slate-300">Monitoring a podpora</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold gradient-text mb-2">100%</div>
                      <div className="text-sm text-slate-300">Záruka spokojenosti</div>
                    </div>
                  </div>

                  <div className="p-4 rounded-xl bg-white/5 backdrop-blur-sm">
                    <p className="text-sm text-slate-300">
                      <strong className="text-white">Garance:</strong> Pokud během 90 dnů neuvidíte zlepšení,
                      vrátíme vám 100% investice.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative py-24 sm:py-32 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full blur-3xl float-animation"></div>
        </div>

        <div className="relative z-10 mx-auto max-w-4xl px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold tracking-tight text-white sm:text-6xl font-space mb-6">
            Začněte svou <span className="gradient-text">transformaci</span> dnes
          </h2>

          <p className="mx-auto max-w-2xl text-xl leading-8 text-slate-300 mb-12">
            Nečekejte na konkurenci. Získejte náskok s kompletním marketingovým ekosystémem.
          </p>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-6">
            <Link
              href="/kontakt"
              className="group relative px-10 py-4 text-lg font-semibold bg-white text-slate-900 rounded-2xl hover:bg-slate-100 transition-all duration-300 hover:scale-105 neon-glow shadow-xl"
            >
              <span className="relative z-10 flex items-center gap-2">
                <SparklesIcon className="w-5 h-5" />
                Začít implementaci
              </span>
            </Link>

            <Link
              href="/"
              className="group flex items-center gap-2 px-8 py-4 text-lg font-medium text-white hover:text-purple-300 transition-colors border border-white/20 rounded-2xl hover:border-purple-300"
            >
              Zpět na hlavní stránku
              <ArrowRightIcon className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
