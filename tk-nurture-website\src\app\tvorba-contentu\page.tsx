import Link from 'next/link'
import { 
  ChartBarIcon,
  DevicePhoneMobileIcon,
  PlayIcon,
  HashtagIcon,
  MagnifyingGlassIcon,
  CheckCircleIcon,
  ArrowRightIcon,
  SparklesIcon,
  RocketLaunchIcon
} from '@heroicons/react/24/outline'

const platforms = [
  {
    name: 'Facebook',
    description: 'Největší sociální síť pro B2C i B2B marketing',
    icon: '📘',
    features: ['Organické posty', 'Facebook Ads', 'Stories', 'Live videa', 'Events'],
    gradient: 'from-blue-600 to-blue-800',
    users: '2.9B+'
  },
  {
    name: 'Instagram',
    description: 'Vizuální platforma pro mladší publikum a lifestyle',
    icon: '📷',
    features: ['Feed posty', 'Stories', 'Reels', 'IGTV', 'Shopping'],
    gradient: 'from-pink-500 to-purple-600',
    users: '2B+'
  },
  {
    name: 'TikTok',
    description: '<PERSON>ych<PERSON> rostoucí platforma pro krátká videa',
    icon: '🎵',
    features: ['<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> videa', 'Trendy', 'Hashtag challenges', 'Live streaming'],
    gradient: 'from-black to-red-500',
    users: '1B+'
  },
  {
    name: 'YouTube',
    description: 'Největší video platforma pro dlouhodobý obsah',
    icon: '📺',
    features: ['Dlouhá videa', 'YouTube Shorts', 'Live streaming', 'Monetizace'],
    gradient: 'from-red-500 to-red-700',
    users: '2.7B+'
  },
  {
    name: 'LinkedIn',
    description: 'Profesionální síť pro B2B marketing',
    icon: '💼',
    features: ['Profesionální posty', 'LinkedIn Ads', 'Articles', 'Networking'],
    gradient: 'from-blue-700 to-blue-900',
    users: '900M+'
  },
  {
    name: 'X (Twitter)',
    description: 'Platforma pro rychlé zprávy a diskuse',
    icon: '🐦',
    features: ['Tweety', 'Threads', 'Spaces', 'Twitter Ads'],
    gradient: 'from-slate-800 to-black',
    users: '450M+'
  }
]

const contentTypes = [
  {
    title: 'Video Content',
    description: 'Profesionální videa pro všechny platformy',
    icon: PlayIcon,
    items: ['Produktová videa', 'Tutoriály', 'Behind the scenes', 'Testimonials']
  },
  {
    title: 'Grafický obsah',
    description: 'Vizuálně atraktivní grafiky a infografiky',
    icon: ChartBarIcon,
    items: ['Infografiky', 'Carousely', 'Quote cards', 'Product shots']
  },
  {
    title: 'Textový obsah',
    description: 'Poutavé texty optimalizované pro každou platformu',
    icon: HashtagIcon,
    items: ['Captions', 'Blog posty', 'Newslettery', 'Ad copy']
  },
  {
    title: 'Interaktivní obsah',
    description: 'Obsah, který zapojuje vaše publikum',
    icon: DevicePhoneMobileIcon,
    items: ['Polls', 'Quizy', 'Live sessions', 'Q&A']
  }
]

export default function TvorbaContentu() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
      {/* Hero section */}
      <div className="relative isolate px-6 pt-24 lg:px-8 hero-bg">
        <div className="absolute inset-0 -z-10 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/20 to-cyan-400/20 rounded-full blur-3xl float-animation"></div>
          <div className="absolute top-3/4 right-1/4 w-80 h-80 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full blur-3xl float-animation" style={{animationDelay: '2s'}}></div>
        </div>
        
        <div className="mx-auto max-w-4xl py-32 sm:py-48 lg:py-56 px-4">
          <div className="text-center">
            <div className="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-200/50 mb-8 glass">
              <RocketLaunchIcon className="w-5 h-5 text-blue-600" />
              <span className="text-sm font-semibold text-blue-800">Content Marketing</span>
            </div>
            
            <h1 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-6xl lg:text-7xl font-space leading-tight">
              Tvorba & <span className="gradient-text">Distribuce Contentu</span>
            </h1>
            
            <p className="mt-8 text-lg sm:text-xl leading-relaxed text-slate-600 max-w-3xl mx-auto">
              Strategická tvorba a distribuce obsahu napříč všemi důležitými sociálními sítěmi 
              a vyhledávači pro maximální dosah a engagement.
            </p>
            
            <div className="mt-16 flex flex-col sm:flex-row items-center justify-center gap-6">
              <Link
                href="/kontakt"
                className="group relative px-10 py-4 text-lg font-semibold text-white rounded-2xl btn-futuristic neon-glow overflow-hidden shadow-xl"
              >
                <SparklesIcon className="w-5 h-5 inline mr-2" />
                Začít content strategii
              </Link>
              
              <Link 
                href="/" 
                className="group flex items-center gap-2 px-8 py-4 text-lg font-medium text-slate-700 hover:text-purple-600 transition-colors border border-slate-300 rounded-2xl hover:border-purple-300"
              >
                Zpět na hlavní stránku
                <ArrowRightIcon className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Platforms section */}
      <div className="py-24 sm:py-32 bg-slate-50/50">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center mb-20">
            <h2 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-5xl font-space">
              Dominujte na <span className="gradient-text">všech platformách</span>
            </h2>
            <p className="mt-6 text-xl leading-8 text-slate-600">
              Pokrýváme všechny hlavní sociální sítě a optimalizujeme obsah pro každou z nich
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {platforms.map((platform, index) => (
              <div 
                key={platform.name} 
                className="group relative card-3d"
                style={{animationDelay: `${index * 0.1}s`}}
              >
                <div className={`relative h-full p-8 rounded-3xl bg-gradient-to-br ${platform.gradient} shadow-2xl border border-white/30 overflow-hidden`}>
                  <div className="absolute inset-0 opacity-5">
                    <div className="absolute top-0 right-0 w-32 h-32 bg-white rounded-full -translate-y-16 translate-x-16"></div>
                  </div>
                  
                  <div className="relative z-10">
                    <div className="flex items-center gap-4 mb-6">
                      <div className="text-4xl">{platform.icon}</div>
                      <div>
                        <h3 className="text-xl font-bold text-white">{platform.name}</h3>
                        <p className="text-sm text-white/90 font-medium">{platform.users} uživatelů</p>
                      </div>
                    </div>
                    
                    <p className="text-white/95 mb-8 leading-relaxed font-medium">
                      {platform.description}
                    </p>
                    
                    <div className="mb-8">
                      <h4 className="font-bold text-white mb-4">Co vytváříme:</h4>
                      <div className="space-y-2">
                        {platform.features.map((feature) => (
                          <div key={feature} className="flex items-center text-sm text-white/90 font-medium">
                            <CheckCircleIcon className="h-4 w-4 text-white mr-2 flex-shrink-0" />
                            <span>{feature}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Content Types section */}
      <div className="py-24 sm:py-32 bg-white">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center mb-20">
            <h2 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-5xl font-space">
              Typy <span className="gradient-text">obsahu</span>
            </h2>
            <p className="mt-6 text-xl leading-8 text-slate-600">
              Vytváříme rozmanité typy obsahu pro maximální engagement
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {contentTypes.map((type, index) => (
              <div 
                key={type.title} 
                className="group relative p-8 rounded-3xl bg-gradient-to-br from-slate-50 to-white border border-slate-200/50 hover:border-purple-200/50 transition-all duration-500 card-3d"
                style={{animationDelay: `${index * 0.1}s`}}
              >
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                
                <div className="relative z-10">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <type.icon className="w-6 h-6 text-white" />
                  </div>
                  
                  <h3 className="text-xl font-semibold text-slate-900 mb-3 group-hover:text-purple-700 transition-colors">
                    {type.title}
                  </h3>
                  
                  <p className="text-slate-600 leading-relaxed mb-6">
                    {type.description}
                  </p>
                  
                  <div className="space-y-2">
                    {type.items.map((item) => (
                      <div key={item} className="flex items-center text-sm text-slate-600">
                        <CheckCircleIcon className="h-4 w-4 text-purple-500 mr-2 flex-shrink-0" />
                        <span>{item}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Process section */}
      <div className="py-24 sm:py-32 bg-slate-50/50">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center mb-20">
            <h2 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-5xl font-space">
              Náš <span className="gradient-text">proces</span>
            </h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center p-8 rounded-3xl bg-white shadow-lg border border-slate-200/50">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-xl">1</span>
              </div>
              <h3 className="text-xl font-bold text-slate-900 mb-4">Strategie</h3>
              <p className="text-slate-600">Analyzujeme vaše cíle, publikum a konkurenci pro vytvoření content strategie</p>
            </div>
            
            <div className="text-center p-8 rounded-3xl bg-white shadow-lg border border-slate-200/50">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-xl">2</span>
              </div>
              <h3 className="text-xl font-bold text-slate-900 mb-4">Tvorba</h3>
              <p className="text-slate-600">Vytváříme kvalitní obsah optimalizovaný pro každou platformu</p>
            </div>
            
            <div className="text-center p-8 rounded-3xl bg-white shadow-lg border border-slate-200/50">
              <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-xl">3</span>
              </div>
              <h3 className="text-xl font-bold text-slate-900 mb-4">Optimalizace</h3>
              <p className="text-slate-600">Sledujeme výsledky a kontinuálně optimalizujeme pro lepší performance</p>
            </div>
          </div>
        </div>
      </div>

      {/* CTA section */}
      <div className="relative py-24 sm:py-32 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full blur-3xl float-animation"></div>
        </div>
        
        <div className="relative z-10 mx-auto max-w-4xl px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold tracking-tight text-white sm:text-6xl font-space mb-6">
            Připraveni <span className="gradient-text">dominovat</span> online?
          </h2>
          
          <p className="mx-auto max-w-2xl text-xl leading-8 text-slate-300 mb-12">
            Začněte s content strategií, která vám přinese skutečné výsledky
          </p>
          
          <Link
            href="/kontakt"
            className="group relative px-10 py-4 text-lg font-semibold bg-white text-slate-900 rounded-2xl hover:bg-slate-100 transition-all duration-300 hover:scale-105 neon-glow shadow-xl"
          >
            <span className="relative z-10 flex items-center gap-2">
              <RocketLaunchIcon className="w-5 h-5" />
              Spustit content strategii
            </span>
          </Link>
        </div>
      </div>
    </div>
  )
}
