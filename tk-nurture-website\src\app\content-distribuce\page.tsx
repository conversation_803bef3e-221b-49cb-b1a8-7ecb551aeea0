import Link from 'next/link'
import { 
  VideoCameraIcon,
  ScissorsIcon,
  PaintBrushIcon,
  ScaleIcon,
  MagnifyingGlassIcon,
  CheckCircleIcon,
  ArrowRightIcon,
  SparklesIcon,
  UserGroupIcon
} from '@heroicons/react/24/outline'

const teamMembers = [
  {
    role: '<PERSON><PERSON><PERSON>',
    description: 'Profesionální natáčení videí, fotografování produktů a eventů',
    icon: VideoCameraIcon,
    skills: ['4K video produkce', 'Produktové fotografie', 'Event coverage', 'Drone záběry'],
    gradient: 'from-red-500 to-orange-500'
  },
  {
    role: 'Střihač',
    description: 'Postprodukce videí, animace a grafické efekty',
    icon: ScissorsIcon,
    skills: ['Video editing', 'Motion graphics', 'Color grading', 'Audio mastering'],
    gradient: 'from-blue-500 to-cyan-500'
  },
  {
    role: 'Grafik',
    description: 'Vizu<PERSON>ln<PERSON> identita, grafické materiály a web design',
    icon: PaintBrushIcon,
    skills: ['Logo design', 'Brand identity', 'Print design', 'UI/UX design'],
    gradient: 'from-purple-500 to-pink-500'
  },
  {
    role: 'Právník',
    description: 'Právní ochrana obsahu, smlouvy a compliance',
    icon: ScaleIcon,
    skills: ['Copyright ochrana', 'GDPR compliance', 'Smluvní právo', 'Trademark'],
    gradient: 'from-green-500 to-emerald-500'
  },
  {
    role: 'SEO Expert',
    description: 'Optimalizace pro vyhledávače a analytika',
    icon: MagnifyingGlassIcon,
    skills: ['Keyword research', 'Technical SEO', 'Analytics', 'Content strategy'],
    gradient: 'from-yellow-500 to-amber-500'
  }
]

const benefits = [
  'Kompletní tým pod jednou střechou',
  'Konzistentní kvalita napříč všemi kanály',
  'Rychlá komunikace a koordinace',
  'Úspora času a nákladů',
  'Profesionální výsledky',
  'Dlouhodobá strategie'
]

export default function ContentDistribuce() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
      {/* Hero section */}
      <div className="relative isolate px-6 pt-24 lg:px-8 hero-bg">
        {/* Animated background elements */}
        <div className="absolute inset-0 -z-10 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full blur-3xl float-animation"></div>
          <div className="absolute top-3/4 right-1/4 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-cyan-400/20 rounded-full blur-3xl float-animation" style={{animationDelay: '2s'}}></div>
        </div>
        
        <div className="mx-auto max-w-4xl py-32 sm:py-48 lg:py-56 px-4">
          <div className="text-center">
            <div className="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-200/50 mb-8 glass">
              <UserGroupIcon className="w-5 h-5 text-purple-600" />
              <span className="text-sm font-semibold text-purple-800">Profesionální tým</span>
            </div>
            
            <h1 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-6xl lg:text-7xl font-space leading-tight">
              Vedení & <span className="gradient-text">Content Distribuce</span>
            </h1>
            
            <p className="mt-8 text-lg sm:text-xl leading-relaxed text-slate-600 max-w-3xl mx-auto">
              Kompletní tým specialistů, kteří se postarají o každý aspekt vašeho obsahu - 
              od natáčení přes právní ochranu až po SEO optimalizaci.
            </p>
            
            <div className="mt-16 flex flex-col sm:flex-row items-center justify-center gap-6">
              <Link
                href="/kontakt"
                className="group relative px-10 py-4 text-lg font-semibold text-white rounded-2xl btn-futuristic neon-glow overflow-hidden shadow-xl"
              >
                <SparklesIcon className="w-5 h-5 inline mr-2" />
                Získat tým expertů
              </Link>
              
              <Link 
                href="/" 
                className="group flex items-center gap-2 px-8 py-4 text-lg font-medium text-slate-700 hover:text-purple-600 transition-colors border border-slate-300 rounded-2xl hover:border-purple-300"
              >
                Zpět na hlavní stránku
                <ArrowRightIcon className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Team section */}
      <div className="py-24 sm:py-32 bg-slate-50/50">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center mb-20">
            <h2 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-5xl font-space">
              Váš <span className="gradient-text">dream team</span> specialistů
            </h2>
            <p className="mt-6 text-xl leading-8 text-slate-600">
              Každý člen našeho týmu je expert ve svém oboru s mnohaletou praxí
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {teamMembers.map((member, index) => (
              <div 
                key={member.role} 
                className="group relative card-3d"
                style={{animationDelay: `${index * 0.1}s`}}
              >
                <div className={`relative h-full p-8 rounded-3xl bg-gradient-to-br ${member.gradient} shadow-2xl border border-white/30 overflow-hidden`}>
                  {/* Background pattern */}
                  <div className="absolute inset-0 opacity-5">
                    <div className="absolute top-0 right-0 w-32 h-32 bg-white rounded-full -translate-y-16 translate-x-16"></div>
                    <div className="absolute bottom-0 left-0 w-24 h-24 bg-white rounded-full translate-y-12 -translate-x-12"></div>
                  </div>
                  
                  {/* Content */}
                  <div className="relative z-10">
                    <div className="flex items-center gap-4 mb-6">
                      <div className="p-4 rounded-2xl bg-white/25 backdrop-blur-sm shadow-lg">
                        <member.icon className="h-8 w-8 text-white" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-white">{member.role}</h3>
                      </div>
                    </div>
                    
                    <p className="text-white/95 mb-8 leading-relaxed font-medium">
                      {member.description}
                    </p>
                    
                    <div className="mb-8">
                      <h4 className="font-bold text-white mb-4">Specializace:</h4>
                      <div className="space-y-2">
                        {member.skills.map((skill) => (
                          <div key={skill} className="flex items-center text-sm text-white/90 font-medium">
                            <CheckCircleIcon className="h-4 w-4 text-white mr-2 flex-shrink-0" />
                            <span>{skill}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Benefits section */}
      <div className="py-24 sm:py-32 bg-white">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-5xl font-space mb-8">
                Proč <span className="gradient-text">kompletní tým</span>?
              </h2>
              
              <div className="space-y-4">
                {benefits.map((benefit, index) => (
                  <div 
                    key={benefit} 
                    className="flex items-center p-4 rounded-2xl bg-gradient-to-r from-slate-50 to-white border border-slate-200/50 hover:border-purple-200/50 transition-all duration-300"
                    style={{animationDelay: `${index * 0.1}s`}}
                  >
                    <CheckCircleIcon className="h-6 w-6 text-purple-500 mr-4 flex-shrink-0" />
                    <span className="text-slate-700 font-medium">{benefit}</span>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="relative">
              <div className="p-12 rounded-3xl bg-gradient-to-br from-slate-900 to-slate-800 text-white overflow-hidden">
                {/* Background decoration */}
                <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full blur-3xl -translate-y-32 translate-x-32"></div>
                
                <div className="relative z-10">
                  <h3 className="text-2xl font-bold mb-6">Jak to funguje?</h3>
                  
                  <div className="space-y-6">
                    <div className="flex items-start gap-4">
                      <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-sm font-bold">1</div>
                      <div>
                        <h4 className="font-semibold mb-2">Analýza potřeb</h4>
                        <p className="text-slate-300 text-sm">Zjistíme, jaký obsah potřebujete a pro které kanály</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start gap-4">
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-sm font-bold">2</div>
                      <div>
                        <h4 className="font-semibold mb-2">Sestavení týmu</h4>
                        <p className="text-slate-300 text-sm">Přiřadíme vám specialisty podle vašich požadavků</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start gap-4">
                      <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-sm font-bold">3</div>
                      <div>
                        <h4 className="font-semibold mb-2">Realizace</h4>
                        <p className="text-slate-300 text-sm">Tým pracuje koordinovaně na vašem obsahu</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* CTA section */}
      <div className="relative py-24 sm:py-32 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full blur-3xl float-animation"></div>
        </div>
        
        <div className="relative z-10 mx-auto max-w-4xl px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold tracking-tight text-white sm:text-6xl font-space mb-6">
            Připraveni na <span className="gradient-text">profesionální obsah</span>?
          </h2>
          
          <p className="mx-auto max-w-2xl text-xl leading-8 text-slate-300 mb-12">
            Získejte tým expertů, který vám vytvoří obsah na úrovni velkých korporací
          </p>
          
          <Link
            href="/kontakt"
            className="group relative px-10 py-4 text-lg font-semibold bg-white text-slate-900 rounded-2xl hover:bg-slate-100 transition-all duration-300 hover:scale-105 neon-glow shadow-xl"
          >
            <span className="relative z-10 flex items-center gap-2">
              <UserGroupIcon className="w-5 h-5" />
              Sestavit můj tým
            </span>
          </Link>
        </div>
      </div>
    </div>
  )
}
