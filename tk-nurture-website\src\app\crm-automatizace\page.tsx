import Link from 'next/link'
import { 
  BoltIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  CircleStackIcon,
  MagnifyingGlassIcon,
  CogIcon,
  CheckCircleIcon,
  ArrowRightIcon,
  SparklesIcon,
  ChartBarIcon,
  ClockIcon,
  UserGroupIcon,
  TrophyIcon
} from '@heroicons/react/24/outline'

const automationUseCases = [
  {
    title: 'Lead Management',
    description: 'Automaticky zpracovávejte a kvalifikujte nové leady z různých zdrojů',
    icon: UserGroupIcon,
    benefits: ['Okamžité zachycení leadů', 'Automatická kvalifikace', 'Přiřazení k sales týmu', 'Follow-up sekvence'],
    gradient: 'from-blue-500 to-cyan-500',
    stats: '3x rychlejší zpracování'
  },
  {
    title: 'Email Marketing Automation',
    description: 'Personalizované email kampaně založené na chování zákazníků',
    icon: ChatBubbleLeftRightIcon,
    benefits: ['Behavioral triggery', 'Segmentace audience', 'A/B testing', 'Performance tracking'],
    gradient: 'from-purple-500 to-pink-500',
    stats: '45% vyšší open rate'
  },
  {
    title: 'Sales Pipeline Automation',
    description: 'Automatizujte celý sales proces od prvního kontaktu po uzavření obchodu',
    icon: TrophyIcon,
    benefits: ['Automatické task assignment', 'Deal progression tracking', 'Forecast reporting', 'Win/loss analysis'],
    gradient: 'from-green-500 to-emerald-500',
    stats: '60% kratší sales cyklus'
  },
  {
    title: 'Customer Support Automation',
    description: 'Inteligentní chatboty a automatické ticket routing pro lepší zákaznickou podporu',
    icon: ChatBubbleLeftRightIcon,
    benefits: ['24/7 dostupnost', 'Instant responses', 'Ticket prioritization', 'Escalation rules'],
    gradient: 'from-orange-500 to-red-500',
    stats: '80% rychlejší řešení'
  },
  {
    title: 'Marketing Analytics & Reporting',
    description: 'Automatické generování reportů a insights z vašich marketingových dat',
    icon: ChartBarIcon,
    benefits: ['Real-time dashboards', 'Custom reports', 'ROI tracking', 'Performance alerts'],
    gradient: 'from-indigo-500 to-purple-500',
    stats: '90% úspora času na reporting'
  },
  {
    title: 'Social Media Management',
    description: 'Automatické plánování, publikování a monitoring sociálních sítí',
    icon: SparklesIcon,
    benefits: ['Content scheduling', 'Cross-platform posting', 'Engagement monitoring', 'Hashtag optimization'],
    gradient: 'from-pink-500 to-rose-500',
    stats: '5x více engagement'
  }
]

const benefits = [
  {
    icon: ClockIcon,
    title: 'Zvýšená efektivita',
    description: 'Automatizujte opakující se úkoly a ušetřete čas pro strategické aktivity'
  },
  {
    icon: CheckCircleIcon,
    title: 'Vyšší přesnost',
    description: 'Minimalizujte lidské chyby a zajistěte konzistentní výkon napříč kampaněmi'
  },
  {
    icon: UserGroupIcon,
    title: 'Personalizace na další úrovni',
    description: 'Doručujte personalizované zážitky pomocí dat-driven insights a automatické segmentace'
  },
  {
    icon: ChartBarIcon,
    title: 'Měřitelný ROI',
    description: 'Sledujte výkonnost v reálném čase a optimalizujte pro maximální návratnost investic'
  }
]

export default function CRMAutomatizace() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
      {/* Hero Section */}
      <section className="relative pt-20 pb-32 flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900"></div>
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%239C92AC" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="animate-fade-in-up">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-purple-500/20 border border-purple-500/30 mb-8">
              <BoltIcon className="w-4 h-4 text-purple-300 mr-2" />
              <span className="text-purple-300 text-sm font-medium">Marketing Automation</span>
            </div>
            
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6">
              CRM a
              <span className="block bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                Automatizace
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-slate-300 mb-12 max-w-4xl mx-auto leading-relaxed">
              Automatizujte marketing, zrychlete růst. Využijte sílu automatizace k odemknutí 
              skryté hodnoty ve vašich stávajících procesech.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/kontakt" className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:from-purple-700 hover:to-pink-700 transition-all duration-300 shadow-lg hover:shadow-purple-500/25 hover:scale-105">
                Začít automatizaci
              </Link>
              <button className="border-2 border-purple-500 text-purple-300 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-purple-500/10 transition-all duration-300">
                Prohlédnout use cases
              </button>
            </div>
          </div>
        </div>
        
        {/* Floating elements */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-purple-500/20 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-32 h-32 bg-pink-500/20 rounded-full blur-xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-blue-500/20 rounded-full blur-xl animate-pulse delay-500"></div>
      </section>

      {/* How Marketing Automation Works */}
      <section className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center mb-20">
            <h2 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-5xl font-space">
              Jak funguje <span className="gradient-text">marketing automatizace</span>?
            </h2>
            <p className="mt-6 text-xl leading-8 text-slate-600">
              Marketing automatizace zjednodušuje marketingové procesy automatizací opakujících se úkolů 
              a doručováním personalizovaných zpráv správnému publiku ve správný čas.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <div key={benefit.title} className="group relative p-8 rounded-3xl bg-gradient-to-br from-slate-50 to-white border border-slate-200/50 hover:border-purple-200/50 transition-all duration-500 card-3d" style={{animationDelay: `${index * 0.1}s`}}>
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <benefit.icon className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-slate-900 mb-3 group-hover:text-purple-700 transition-colors">{benefit.title}</h3>
                  <p className="text-slate-600 leading-relaxed">{benefit.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-24 sm:py-32 bg-slate-50/50">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center mb-20">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-200/50 mb-6">
              <CogIcon className="w-4 h-4 text-blue-500" />
              <span className="text-sm font-medium text-blue-700">Use Cases</span>
            </div>
            <h2 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-5xl font-space">
              Prozkoumejte <span className="gradient-text">automatizační řešení</span>
            </h2>
            <p className="mt-6 text-xl leading-8 text-slate-600">
              Konkrétní způsoby, jak automatizace transformuje vaše marketingové procesy
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {automationUseCases.map((useCase, index) => (
              <div key={useCase.title} className="group relative card-3d" style={{animationDelay: `${index * 0.1}s`}}>
                <div className={`relative h-full p-8 rounded-3xl bg-gradient-to-br ${useCase.gradient} shadow-2xl border border-white/30 overflow-hidden`}>
                  <div className="absolute inset-0 opacity-5">
                    <div className="absolute top-0 right-0 w-32 h-32 bg-white rounded-full -translate-y-16 translate-x-16"></div>
                    <div className="absolute bottom-0 left-0 w-24 h-24 bg-white rounded-full translate-y-12 -translate-x-12"></div>
                  </div>
                  
                  <div className="relative z-10">
                    <div className="flex items-center gap-4 mb-6">
                      <div className="p-4 rounded-2xl bg-white/25 backdrop-blur-sm shadow-lg">
                        <useCase.icon className="h-8 w-8 text-white" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-white">{useCase.title}</h3>
                        <p className="text-sm text-white/90 font-medium">{useCase.stats}</p>
                      </div>
                    </div>
                    
                    <p className="text-white/95 mb-8 leading-relaxed font-medium">
                      {useCase.description}
                    </p>
                    
                    <div className="mb-8">
                      <h4 className="font-bold text-white mb-4">Klíčové výhody:</h4>
                      <div className="space-y-2">
                        {useCase.benefits.map((benefit) => (
                          <div key={benefit} className="flex items-center text-sm text-white/90 font-medium">
                            <CheckCircleIcon className="h-4 w-4 text-white mr-2 flex-shrink-0" />
                            <span>{benefit}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    <Link href="/kontakt" className="group/link inline-flex items-center gap-2 px-6 py-3 bg-white/30 hover:bg-white/40 backdrop-blur-sm rounded-xl text-white font-semibold transition-all duration-300 hover:scale-105 shadow-lg">
                      Zjistit více
                      <ArrowRightIcon className="w-4 h-4 group-hover/link:translate-x-1 transition-transform" />
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How to Start Section */}
      <section className="py-24 sm:py-32 bg-white">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center mb-20">
            <h2 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-5xl font-space">
              Jak automatizovat vaše <span className="gradient-text">marketingové procesy</span>
            </h2>
            <p className="mt-6 text-xl leading-8 text-slate-600">
              Připraveni začít sklízet výhody automatizace? Máme vše, co potřebujete k začátku.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center p-8 rounded-3xl bg-gradient-to-br from-slate-50 to-white border border-slate-200/50 hover:border-purple-200/50 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-xl">1</span>
              </div>
              <h3 className="text-xl font-semibold text-slate-900 mb-3">Identifikujte správné úkoly</h3>
              <p className="text-slate-600">Začněte identifikací opakujících se marketingových aktivit, které můžete zjednodušit</p>
            </div>
            
            <div className="text-center p-8 rounded-3xl bg-gradient-to-br from-slate-50 to-white border border-slate-200/50 hover:border-purple-200/50 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-xl">2</span>
              </div>
              <h3 className="text-xl font-semibold text-slate-900 mb-3">Vyberte správný software</h3>
              <p className="text-slate-600">Zvolte spolehlivý nástroj pro automatizaci, který vyhovuje vašim potřebám a rozpočtu</p>
            </div>
            
            <div className="text-center p-8 rounded-3xl bg-gradient-to-br from-slate-50 to-white border border-slate-200/50 hover:border-purple-200/50 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-xl">3</span>
              </div>
              <h3 className="text-xl font-semibold text-slate-900 mb-3">Prozkoumejte šablony</h3>
              <p className="text-slate-600">Využijte připravené šablony pro rychlé řešení běžných automatizačních úkolů</p>
            </div>
            
            <div className="text-center p-8 rounded-3xl bg-gradient-to-br from-slate-50 to-white border border-slate-200/50 hover:border-purple-200/50 transition-all duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-xl">4</span>
              </div>
              <h3 className="text-xl font-semibold text-slate-900 mb-3">Vytvořte workflow</h3>
              <p className="text-slate-600">Navrhněte krok za krokem workflow, které automatizuje vaše vybrané úkoly</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative py-24 sm:py-32 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full blur-3xl float-animation"></div>
          <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-full blur-3xl float-animation" style={{animationDelay: '3s'}}></div>
        </div>
        
        <div className="relative z-10 mx-auto max-w-4xl px-6 lg:px-8 text-center">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 mb-8">
            <SparklesIcon className="w-4 h-4 text-purple-300" />
            <span className="text-sm font-medium text-purple-200">Připraveni na změnu?</span>
          </div>
          
          <h2 className="text-4xl font-bold tracking-tight text-white sm:text-6xl font-space mb-6">
            Realizujte plný <span className="gradient-text">potenciál</span> vašeho podnikání
          </h2>
          
          <p className="mx-auto max-w-2xl text-xl leading-8 text-slate-300 mb-12">
            Získejte bezplatnou konzultaci a zjistěte, jak automatizace může transformovat 
            vaše marketingové procesy a urychlit růst.
          </p>
          
          <div className="flex flex-col sm:flex-row items-center justify-center gap-6">
            <Link href="/kontakt" className="group relative px-8 py-4 text-lg font-semibold bg-white text-slate-900 rounded-2xl hover:bg-slate-100 transition-all duration-300 hover:scale-105 neon-glow">
              <span className="relative z-10 flex items-center gap-2">
                <BoltIcon className="w-5 h-5" />
                Začít automatizaci zdarma
              </span>
            </Link>
            <Link href="/nas-proces-implementace" className="group flex items-center gap-2 px-6 py-3 text-lg font-medium text-white hover:text-purple-300 transition-colors">
              Prohlédnout náš proces
              <ArrowRightIcon className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
