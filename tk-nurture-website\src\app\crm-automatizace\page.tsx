import Link from 'next/link'

export default function CRMAutomatizace() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
      <section className="relative pt-20 pb-32 flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900"></div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="animate-fade-in-up">
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6">
              CRM a
              <span className="block bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                Automatizace
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-slate-300 mb-12 max-w-4xl mx-auto leading-relaxed">
              Automatizujte marketing, zrychlete růst. Využijte sílu automatizace k odemknutí 
              skryté hodnoty ve vašich stávajících procesech.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/kontakt" className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:from-purple-700 hover:to-pink-700 transition-all duration-300 shadow-lg hover:shadow-purple-500/25 hover:scale-105">
                Začít automatizaci
              </Link>
              <button className="border-2 border-purple-500 text-purple-300 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-purple-500/10 transition-all duration-300">
                Prohlédnout use cases
              </button>
            </div>
          </div>
        </div>
      </section>

      <section className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center mb-20">
            <h2 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-5xl font-space">
              Jak funguje <span className="gradient-text">marketing automatizace</span>?
            </h2>
            <p className="mt-6 text-xl leading-8 text-slate-600">
              Marketing automatizace zjednodušuje marketingové procesy automatizací opakujících se úkolů 
              a doručováním personalizovaných zpráv správnému publiku ve správný čas.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="group relative p-8 rounded-3xl bg-gradient-to-br from-slate-50 to-white border border-slate-200/50 hover:border-purple-200/50 transition-all duration-500 card-3d">
              <div className="relative z-10">
                <h3 className="text-xl font-semibold text-slate-900 mb-3">Zvýšená efektivita</h3>
                <p className="text-slate-600 leading-relaxed">Automatizujte opakující se úkoly a ušetřete čas pro strategické aktivity</p>
              </div>
            </div>

            <div className="group relative p-8 rounded-3xl bg-gradient-to-br from-slate-50 to-white border border-slate-200/50 hover:border-purple-200/50 transition-all duration-500 card-3d">
              <div className="relative z-10">
                <h3 className="text-xl font-semibold text-slate-900 mb-3">Vyšší přesnost</h3>
                <p className="text-slate-600 leading-relaxed">Minimalizujte lidské chyby a zajistěte konzistentní výkon napříč kampaněmi</p>
              </div>
            </div>

            <div className="group relative p-8 rounded-3xl bg-gradient-to-br from-slate-50 to-white border border-slate-200/50 hover:border-purple-200/50 transition-all duration-500 card-3d">
              <div className="relative z-10">
                <h3 className="text-xl font-semibold text-slate-900 mb-3">Personalizace</h3>
                <p className="text-slate-600 leading-relaxed">Doručujte personalizované zážitky pomocí dat-driven insights a automatické segmentace</p>
              </div>
            </div>

            <div className="group relative p-8 rounded-3xl bg-gradient-to-br from-slate-50 to-white border border-slate-200/50 hover:border-purple-200/50 transition-all duration-500 card-3d">
              <div className="relative z-10">
                <h3 className="text-xl font-semibold text-slate-900 mb-3">Měřitelný ROI</h3>
                <p className="text-slate-600 leading-relaxed">Sledujte výkonnost v reálném čase a optimalizujte pro maximální návratnost investic</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-24 sm:py-32 bg-slate-50/50">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-3xl text-center mb-20">
            <h2 className="text-4xl font-bold tracking-tight text-slate-900 sm:text-5xl font-space">
              Prozkoumejte <span className="gradient-text">automatizační řešení</span>
            </h2>
            <p className="mt-6 text-xl leading-8 text-slate-600">
              Konkrétní způsoby, jak automatizace transformuje vaše marketingové procesy
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="group relative card-3d">
              <div className="relative h-full p-8 rounded-3xl bg-gradient-to-br from-blue-500 to-cyan-500 shadow-2xl border border-white/30 overflow-hidden">
                <div className="relative z-10">
                  <h3 className="text-xl font-bold text-white mb-4">Lead Management</h3>
                  <p className="text-white/95 mb-8 leading-relaxed font-medium">
                    Automaticky zpracovávejte a kvalifikujte nové leady z různých zdrojů
                  </p>
                  <p className="text-sm text-white/90 font-medium">3x rychlejší zpracování</p>
                </div>
              </div>
            </div>

            <div className="group relative card-3d">
              <div className="relative h-full p-8 rounded-3xl bg-gradient-to-br from-purple-500 to-pink-500 shadow-2xl border border-white/30 overflow-hidden">
                <div className="relative z-10">
                  <h3 className="text-xl font-bold text-white mb-4">Email Marketing Automation</h3>
                  <p className="text-white/95 mb-8 leading-relaxed font-medium">
                    Personalizované email kampaně založené na chování zákazníků
                  </p>
                  <p className="text-sm text-white/90 font-medium">45% vyšší open rate</p>
                </div>
              </div>
            </div>

            <div className="group relative card-3d">
              <div className="relative h-full p-8 rounded-3xl bg-gradient-to-br from-green-500 to-emerald-500 shadow-2xl border border-white/30 overflow-hidden">
                <div className="relative z-10">
                  <h3 className="text-xl font-bold text-white mb-4">Sales Pipeline Automation</h3>
                  <p className="text-white/95 mb-8 leading-relaxed font-medium">
                    Automatizujte celý sales proces od prvního kontaktu po uzavření obchodu
                  </p>
                  <p className="text-sm text-white/90 font-medium">60% kratší sales cyklus</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="relative py-24 sm:py-32 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
        <div className="relative z-10 mx-auto max-w-4xl px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold tracking-tight text-white sm:text-6xl font-space mb-6">
            Realizujte plný <span className="gradient-text">potenciál</span> vašeho podnikání
          </h2>
          
          <p className="mx-auto max-w-2xl text-xl leading-8 text-slate-300 mb-12">
            Získejte bezplatnou konzultaci a zjistěte, jak automatizace může transformovat 
            vaše marketingové procesy a urychlit růst.
          </p>
          
          <div className="flex flex-col sm:flex-row items-center justify-center gap-6">
            <Link href="/kontakt" className="group relative px-8 py-4 text-lg font-semibold bg-white text-slate-900 rounded-2xl hover:bg-slate-100 transition-all duration-300 hover:scale-105 neon-glow">
              Začít automatizaci zdarma
            </Link>
            <Link href="/nas-proces-implementace" className="group flex items-center gap-2 px-6 py-3 text-lg font-medium text-white hover:text-purple-300 transition-colors">
              Prohlédnout náš proces
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
