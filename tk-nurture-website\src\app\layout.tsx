import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import Header from '@/components/Header'
import Footer from '@/components/Footer'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'TK Nurture - Marketingová agentura pro malé a střední podniky',
  description: 'Specializujeme se na content marketing, distribuci obsahu a komplexní online přítomnost pro malé a střední podniky. Pomáháme vám růst s minimálními starostmi.',
  keywords: 'marketing, content marketing, sociální sítě, SEO, ma<PERSON> podniky, středn<PERSON> podniky, online marketing',
  authors: [{ name: 'TK Nurture' }],
  viewport: 'width=device-width, initial-scale=1',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="cs">
      <body className={inter.className}>
        <Header />
        <main className="min-h-screen">
          {children}
        </main>
        <Footer />

        {/* Cal.com embed script */}
        <script
          type="text/javascript"
          dangerouslySetInnerHTML={{
            __html: `
              (function (C, A, L) {
                let p = function (a, ar) { a.q.push(ar); };
                let d = C.document;
                C.Cal = C.Cal || function () {
                  let cal = C.Cal;
                  let ar = arguments;
                  if (!cal.loaded) {
                    cal.ns = {};
                    cal.q = cal.q || [];
                    d.head.appendChild(d.createElement("script")).src = A;
                    cal.loaded = true;
                  }
                  if (ar[0] === L) {
                    const api = function () { p(api, arguments); };
                    const namespace = ar[1];
                    api.q = api.q || [];
                    if(typeof namespace === "string"){
                      cal.ns[namespace] = cal.ns[namespace] || api;
                      p(cal.ns[namespace], ar);
                      p(cal, ["initNamespace", namespace]);
                    } else p(cal, ar);
                    return;
                  }
                  p(cal, ar);
                };
              })(window, "https://app.cal.com/embed/embed.js", "init");

              Cal("init", "hovor-s-tknurture", {origin:"https://app.cal.com"});
              Cal.ns["hovor-s-tknurture"]("ui", {
                "cssVarsPerTheme": {
                  "light": {"cal-brand": "#000000"},
                  "dark": {"cal-brand": "#555555"}
                },
                "hideEventTypeDetails": false,
                "layout": "month_view"
              });
            `
          }}
        />
      </body>
    </html>
  )
}
