/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/kontakt/page";
exports.ids = ["app/kontakt/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fkontakt%2Fpage&page=%2Fkontakt%2Fpage&appPaths=%2Fkontakt%2Fpage&pagePath=private-next-app-dir%2Fkontakt%2Fpage.tsx&appDir=C%3A%5CUsers%5Cspajk%5CDocuments%5Caugment-projects%5Cnew%20web%5Ctk-nurture-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cspajk%5CDocuments%5Caugment-projects%5Cnew%20web%5Ctk-nurture-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fkontakt%2Fpage&page=%2Fkontakt%2Fpage&appPaths=%2Fkontakt%2Fpage&pagePath=private-next-app-dir%2Fkontakt%2Fpage.tsx&appDir=C%3A%5CUsers%5Cspajk%5CDocuments%5Caugment-projects%5Cnew%20web%5Ctk-nurture-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cspajk%5CDocuments%5Caugment-projects%5Cnew%20web%5Ctk-nurture-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/kontakt/page.tsx */ \"(rsc)/./src/app/kontakt/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'kontakt',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/kontakt/page\",\n        pathname: \"/kontakt\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fkontakt%2Fpage&page=%2Fkontakt%2Fpage&appPaths=%2Fkontakt%2Fpage&pagePath=private-next-app-dir%2Fkontakt%2Fpage.tsx&appDir=C%3A%5CUsers%5Cspajk%5CDocuments%5Caugment-projects%5Cnew%20web%5Ctk-nurture-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cspajk%5CDocuments%5Caugment-projects%5Cnew%20web%5Ctk-nurture-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Header.tsx */ \"(rsc)/./src/components/Header.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Csrc%5C%5Capp%5C%5Ckontakt%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Csrc%5C%5Capp%5C%5Ckontakt%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/kontakt/page.tsx */ \"(rsc)/./src/app/kontakt/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NwYWprJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q25ldyUyMHdlYiU1QyU1Q3RrLW51cnR1cmUtd2Vic2l0ZSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2tvbnRha3QlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0tBQTRJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxzcGFqa1xcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxuZXcgd2ViXFxcXHRrLW51cnR1cmUtd2Vic2l0ZVxcXFxzcmNcXFxcYXBwXFxcXGtvbnRha3RcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Csrc%5C%5Capp%5C%5Ckontakt%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"883b4dc2a69d\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNwYWprXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXG5ldyB3ZWJcXHRrLW51cnR1cmUtd2Vic2l0ZVxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiODgzYjRkYzJhNjlkXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/kontakt/page.tsx":
/*!**********************************!*\
  !*** ./src/app/kontakt/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\new web\\tk-nurture-website\\src\\app\\kontakt\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Header */ \"(rsc)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Footer */ \"(rsc)/./src/components/Footer.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: 'TK Nurture - Marketingová agentura pro malé a střední podniky',\n    description: 'Specializujeme se na content marketing, distribuci obsahu a komplexní online přítomnost pro malé a střední podniky. Pomáháme vám růst s minimálními starostmi.',\n    keywords: 'marketing, content marketing, sociální sítě, SEO, malé podniky, střední podniky, online marketing',\n    authors: [\n        {\n            name: 'TK Nurture'\n        }\n    ],\n    viewport: 'width=device-width, initial-scale=1'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"cs\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"min-h-screen\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst navigation = {\n    services: [\n        {\n            name: 'Content distribuce',\n            href: '/content-distribuce'\n        },\n        {\n            name: 'Tvorba contentu',\n            href: '/tvorba-contentu'\n        },\n        {\n            name: 'Web & CRM',\n            href: '/web-crm'\n        }\n    ],\n    company: [\n        {\n            name: 'O nás',\n            href: '/o-nas'\n        },\n        {\n            name: 'Kontakt',\n            href: '/kontakt'\n        },\n        {\n            name: 'Blog',\n            href: '/blog'\n        }\n    ],\n    social: [\n        {\n            name: 'Facebook',\n            href: '#',\n            icon: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            name: 'Instagram',\n            href: '#',\n            icon: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M12.017 0C8.396 0 7.989.013 7.041.048 6.094.082 5.52.204 5.036.388a3.892 3.892 0 0 0-1.407.923A3.892 3.892 0 0 0 2.706 2.72c-.184.484-.306 1.058-.34 2.005C2.331 5.673 2.318 6.08 2.318 9.701v4.598c0 3.621.013 4.028.048 4.976.034.947.156 1.521.34 2.005a3.892 3.892 0 0 0 .923 1.407 3.892 3.892 0 0 0 1.407.923c.484.184 1.058.306 2.005.34.948.035 1.355.048 4.976.048h4.598c3.621 0 4.028-.013 4.976-.048.947-.034 1.521-.156 2.005-.34a3.892 3.892 0 0 0 1.407-.923 3.892 3.892 0 0 0 .923-1.407c.184-.484.306-1.058.34-2.005.035-.948.048-1.355.048-4.976V9.701c0-3.621-.013-4.028-.048-4.976-.034-.947-.156-1.521-.34-2.005a3.892 3.892 0 0 0-.923-1.407A3.892 3.892 0 0 0 19.982 2.72c-.484-.184-1.058-.306-2.005-.34C17.029.013 16.622 0 13.001 0h-1.984zm-.717 1.442h2.004c3.136 0 3.506.011 4.72.042.878.034 1.354.155 1.671.257.42.163.72.358 1.035.673.315.315.51.615.673 1.035.102.317.223.793.257 1.671.031 1.214.042 1.584.042 4.72v.717c0 3.136-.011 3.506-.042 4.72-.034.878-.155 1.354-.257 1.671a2.45 2.45 0 0 1-.673 1.035 2.45 2.45 0 0 1-1.035.673c-.317.102-.793.223-1.671.257-1.214.031-1.584.042-4.72.042H9.283c-3.136 0-3.506-.011-4.72-.042-.878-.034-1.354-.155-1.671-.257a2.45 2.45 0 0 1-1.035-.673 2.45 2.45 0 0 1-.673-1.035c-.102-.317-.223-.793-.257-1.671-.031-1.214-.042-1.584-.042-4.72V9.283c0-3.136.011-3.506.042-4.72.034-.878.155-1.354.257-1.671.163-.42.358-.72.673-1.035a2.45 2.45 0 0 1 1.035-.673c.317-.102.793-.223 1.671-.257 1.214-.031 1.584-.042 4.72-.042z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            name: 'LinkedIn',\n            href: '#',\n            icon: (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    ...props,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, undefined)\n        }\n    ]\n};\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900\",\n        \"aria-labelledby\": \"footer-heading\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                id: \"footer-heading\",\n                className: \"sr-only\",\n                children: \"Footer\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto max-w-7xl px-6 pb-8 pt-16 sm:pt-24 lg:px-8 lg:pt-32\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"xl:grid xl:grid-cols-3 xl:gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: \"TK Nurture\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 63,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-300 mt-2\",\n                                                children: \"Troup & Koubek\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm leading-6 text-gray-300\",\n                                        children: \"Specializujeme se na komplexn\\xed marketingov\\xe9 služby pro mal\\xe9 a středn\\xed podniky. Pom\\xe1h\\xe1me v\\xe1m růst s minim\\xe1ln\\xedmi starostmi a maxim\\xe1ln\\xed efektivitou.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-6\",\n                                        children: navigation.social.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: item.href,\n                                                className: \"text-gray-400 hover:text-gray-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sr-only\",\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 73,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        className: \"h-6 w-6\",\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 74,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, item.name, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-16 grid grid-cols-2 gap-8 xl:col-span-2 xl:mt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:grid md:grid-cols-2 md:gap-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-semibold leading-6 text-white\",\n                                                        children: \"Služby\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 82,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        role: \"list\",\n                                                        className: \"mt-6 space-y-4\",\n                                                        children: navigation.services.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                    href: item.href,\n                                                                    className: \"text-sm leading-6 text-gray-300 hover:text-white\",\n                                                                    children: item.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                                    lineNumber: 86,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, item.name, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 85,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 83,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-10 md:mt-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-sm font-semibold leading-6 text-white\",\n                                                        children: \"Společnost\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 94,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        role: \"list\",\n                                                        className: \"mt-6 space-y-4\",\n                                                        children: navigation.company.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                                    href: item.href,\n                                                                    className: \"text-sm leading-6 text-gray-300 hover:text-white\",\n                                                                    children: item.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                                    lineNumber: 98,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, item.name, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 97,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 95,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:grid md:grid-cols-1 md:gap-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-semibold leading-6 text-white\",\n                                                    children: \"Kontakt\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6 space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Email:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                                    lineNumber: 111,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \" <EMAIL>\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 110,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Telefon:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                                    lineNumber: 114,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \" +420 123 456 789\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 113,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Adresa:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                                    lineNumber: 117,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \" Praha, Česk\\xe1 republika\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 116,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-16 border-t border-gray-900/10 pt-8 sm:mt-20 lg:mt-24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs leading-5 text-gray-400\",\n                            children: \"\\xa9 2024 TK Nurture (Troup & Koubek). Všechna pr\\xe1va vyhrazena.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Footer.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9Gb290ZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUE0QjtBQUU1QixNQUFNQyxhQUFhO0lBQ2pCQyxVQUFVO1FBQ1I7WUFBRUMsTUFBTTtZQUFzQkMsTUFBTTtRQUFzQjtRQUMxRDtZQUFFRCxNQUFNO1lBQW1CQyxNQUFNO1FBQW1CO1FBQ3BEO1lBQUVELE1BQU07WUFBYUMsTUFBTTtRQUFXO0tBQ3ZDO0lBQ0RDLFNBQVM7UUFDUDtZQUFFRixNQUFNO1lBQVNDLE1BQU07UUFBUztRQUNoQztZQUFFRCxNQUFNO1lBQVdDLE1BQU07UUFBVztRQUNwQztZQUFFRCxNQUFNO1lBQVFDLE1BQU07UUFBUTtLQUMvQjtJQUNERSxRQUFRO1FBQ047WUFDRUgsTUFBTTtZQUNOQyxNQUFNO1lBQ05HLE1BQU0sQ0FBQ0Msc0JBQ0wsOERBQUNDO29CQUFJQyxNQUFLO29CQUFlQyxTQUFRO29CQUFhLEdBQUdILEtBQUs7OEJBQ3BELDRFQUFDSTt3QkFDQ0MsVUFBUzt3QkFDVEMsR0FBRTt3QkFDRkMsVUFBUzs7Ozs7Ozs7Ozs7UUFJakI7UUFDQTtZQUNFWixNQUFNO1lBQ05DLE1BQU07WUFDTkcsTUFBTSxDQUFDQyxzQkFDTCw4REFBQ0M7b0JBQUlDLE1BQUs7b0JBQWVDLFNBQVE7b0JBQWEsR0FBR0gsS0FBSzs4QkFDcEQsNEVBQUNJO3dCQUNDQyxVQUFTO3dCQUNUQyxHQUFFO3dCQUNGQyxVQUFTOzs7Ozs7Ozs7OztRQUlqQjtRQUNBO1lBQ0VaLE1BQU07WUFDTkMsTUFBTTtZQUNORyxNQUFNLENBQUNDLHNCQUNMLDhEQUFDQztvQkFBSUMsTUFBSztvQkFBZUMsU0FBUTtvQkFBYSxHQUFHSCxLQUFLOzhCQUNwRCw0RUFBQ0k7d0JBQUtFLEdBQUU7Ozs7Ozs7Ozs7O1FBR2Q7S0FDRDtBQUNIO0FBRWUsU0FBU0U7SUFDdEIscUJBQ0UsOERBQUNDO1FBQU9DLFdBQVU7UUFBY0MsbUJBQWdCOzswQkFDOUMsOERBQUNDO2dCQUFHQyxJQUFHO2dCQUFpQkgsV0FBVTswQkFBVTs7Ozs7OzBCQUc1Qyw4REFBQ0k7Z0JBQUlKLFdBQVU7O2tDQUNiLDhEQUFDSTt3QkFBSUosV0FBVTs7MENBQ2IsOERBQUNJO2dDQUFJSixXQUFVOztrREFDYiw4REFBQ0k7OzBEQUNDLDhEQUFDQztnREFBS0wsV0FBVTswREFBZ0M7Ozs7OzswREFDaEQsOERBQUNNO2dEQUFFTixXQUFVOzBEQUE2Qjs7Ozs7Ozs7Ozs7O2tEQUU1Qyw4REFBQ007d0NBQUVOLFdBQVU7a0RBQWtDOzs7Ozs7a0RBSS9DLDhEQUFDSTt3Q0FBSUosV0FBVTtrREFDWmpCLFdBQVdLLE1BQU0sQ0FBQ21CLEdBQUcsQ0FBQyxDQUFDQyxxQkFDdEIsOERBQUNDO2dEQUFrQnZCLE1BQU1zQixLQUFLdEIsSUFBSTtnREFBRWMsV0FBVTs7a0VBQzVDLDhEQUFDSzt3REFBS0wsV0FBVTtrRUFBV1EsS0FBS3ZCLElBQUk7Ozs7OztrRUFDcEMsOERBQUN1QixLQUFLbkIsSUFBSTt3REFBQ1csV0FBVTt3REFBVVUsZUFBWTs7Ozs7OzsrQ0FGckNGLEtBQUt2QixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7OzBDQU92Qiw4REFBQ21CO2dDQUFJSixXQUFVOztrREFDYiw4REFBQ0k7d0NBQUlKLFdBQVU7OzBEQUNiLDhEQUFDSTs7a0VBQ0MsOERBQUNPO3dEQUFHWCxXQUFVO2tFQUE2Qzs7Ozs7O2tFQUMzRCw4REFBQ1k7d0RBQUdDLE1BQUs7d0RBQU9iLFdBQVU7a0VBQ3ZCakIsV0FBV0MsUUFBUSxDQUFDdUIsR0FBRyxDQUFDLENBQUNDLHFCQUN4Qiw4REFBQ007MEVBQ0MsNEVBQUNoQyxrREFBSUE7b0VBQUNJLE1BQU1zQixLQUFLdEIsSUFBSTtvRUFBRWMsV0FBVTs4RUFDOUJRLEtBQUt2QixJQUFJOzs7Ozs7K0RBRkx1QixLQUFLdkIsSUFBSTs7Ozs7Ozs7Ozs7Ozs7OzswREFReEIsOERBQUNtQjtnREFBSUosV0FBVTs7a0VBQ2IsOERBQUNXO3dEQUFHWCxXQUFVO2tFQUE2Qzs7Ozs7O2tFQUMzRCw4REFBQ1k7d0RBQUdDLE1BQUs7d0RBQU9iLFdBQVU7a0VBQ3ZCakIsV0FBV0ksT0FBTyxDQUFDb0IsR0FBRyxDQUFDLENBQUNDLHFCQUN2Qiw4REFBQ007MEVBQ0MsNEVBQUNoQyxrREFBSUE7b0VBQUNJLE1BQU1zQixLQUFLdEIsSUFBSTtvRUFBRWMsV0FBVTs4RUFDOUJRLEtBQUt2QixJQUFJOzs7Ozs7K0RBRkx1QixLQUFLdkIsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFTMUIsOERBQUNtQjt3Q0FBSUosV0FBVTtrREFDYiw0RUFBQ0k7OzhEQUNDLDhEQUFDTztvREFBR1gsV0FBVTs4REFBNkM7Ozs7Ozs4REFDM0QsOERBQUNJO29EQUFJSixXQUFVOztzRUFDYiw4REFBQ007NERBQUVOLFdBQVU7OzhFQUNYLDhEQUFDZTs4RUFBTzs7Ozs7O2dFQUFlOzs7Ozs7O3NFQUV6Qiw4REFBQ1Q7NERBQUVOLFdBQVU7OzhFQUNYLDhEQUFDZTs4RUFBTzs7Ozs7O2dFQUFpQjs7Ozs7OztzRUFFM0IsOERBQUNUOzREQUFFTixXQUFVOzs4RUFDWCw4REFBQ2U7OEVBQU87Ozs7OztnRUFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FPcEMsOERBQUNYO3dCQUFJSixXQUFVO2tDQUNiLDRFQUFDTTs0QkFBRU4sV0FBVTtzQ0FBa0M7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT3pEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNwYWprXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXG5ldyB3ZWJcXHRrLW51cnR1cmUtd2Vic2l0ZVxcc3JjXFxjb21wb25lbnRzXFxGb290ZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluaydcblxuY29uc3QgbmF2aWdhdGlvbiA9IHtcbiAgc2VydmljZXM6IFtcbiAgICB7IG5hbWU6ICdDb250ZW50IGRpc3RyaWJ1Y2UnLCBocmVmOiAnL2NvbnRlbnQtZGlzdHJpYnVjZScgfSxcbiAgICB7IG5hbWU6ICdUdm9yYmEgY29udGVudHUnLCBocmVmOiAnL3R2b3JiYS1jb250ZW50dScgfSxcbiAgICB7IG5hbWU6ICdXZWIgJiBDUk0nLCBocmVmOiAnL3dlYi1jcm0nIH0sXG4gIF0sXG4gIGNvbXBhbnk6IFtcbiAgICB7IG5hbWU6ICdPIG7DoXMnLCBocmVmOiAnL28tbmFzJyB9LFxuICAgIHsgbmFtZTogJ0tvbnRha3QnLCBocmVmOiAnL2tvbnRha3QnIH0sXG4gICAgeyBuYW1lOiAnQmxvZycsIGhyZWY6ICcvYmxvZycgfSxcbiAgXSxcbiAgc29jaWFsOiBbXG4gICAge1xuICAgICAgbmFtZTogJ0ZhY2Vib29rJyxcbiAgICAgIGhyZWY6ICcjJyxcbiAgICAgIGljb246IChwcm9wczogYW55KSA9PiAoXG4gICAgICAgIDxzdmcgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIiB7Li4ucHJvcHN9PlxuICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICBmaWxsUnVsZT1cImV2ZW5vZGRcIlxuICAgICAgICAgICAgZD1cIk0yMiAxMmMwLTUuNTIzLTQuNDc3LTEwLTEwLTEwUzIgNi40NzcgMiAxMmMwIDQuOTkxIDMuNjU3IDkuMTI4IDguNDM4IDkuODc4di02Ljk4N2gtMi41NFYxMmgyLjU0VjkuNzk3YzAtMi41MDYgMS40OTItMy44OSAzLjc3Ny0zLjg5IDEuMDk0IDAgMi4yMzguMTk1IDIuMjM4LjE5NXYyLjQ2aC0xLjI2Yy0xLjI0MyAwLTEuNjMuNzcxLTEuNjMgMS41NjJWMTJoMi43NzNsLS40NDMgMi44OWgtMi4zM3Y2Ljk4OEMxOC4zNDMgMjEuMTI4IDIyIDE2Ljk5MSAyMiAxMnpcIlxuICAgICAgICAgICAgY2xpcFJ1bGU9XCJldmVub2RkXCJcbiAgICAgICAgICAvPlxuICAgICAgICA8L3N2Zz5cbiAgICAgICksXG4gICAgfSxcbiAgICB7XG4gICAgICBuYW1lOiAnSW5zdGFncmFtJyxcbiAgICAgIGhyZWY6ICcjJyxcbiAgICAgIGljb246IChwcm9wczogYW55KSA9PiAoXG4gICAgICAgIDxzdmcgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIiB7Li4ucHJvcHN9PlxuICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICBmaWxsUnVsZT1cImV2ZW5vZGRcIlxuICAgICAgICAgICAgZD1cIk0xMi4wMTcgMEM4LjM5NiAwIDcuOTg5LjAxMyA3LjA0MS4wNDggNi4wOTQuMDgyIDUuNTIuMjA0IDUuMDM2LjM4OGEzLjg5MiAzLjg5MiAwIDAgMC0xLjQwNy45MjNBMy44OTIgMy44OTIgMCAwIDAgMi43MDYgMi43MmMtLjE4NC40ODQtLjMwNiAxLjA1OC0uMzQgMi4wMDVDMi4zMzEgNS42NzMgMi4zMTggNi4wOCAyLjMxOCA5LjcwMXY0LjU5OGMwIDMuNjIxLjAxMyA0LjAyOC4wNDggNC45NzYuMDM0Ljk0Ny4xNTYgMS41MjEuMzQgMi4wMDVhMy44OTIgMy44OTIgMCAwIDAgLjkyMyAxLjQwNyAzLjg5MiAzLjg5MiAwIDAgMCAxLjQwNy45MjNjLjQ4NC4xODQgMS4wNTguMzA2IDIuMDA1LjM0Ljk0OC4wMzUgMS4zNTUuMDQ4IDQuOTc2LjA0OGg0LjU5OGMzLjYyMSAwIDQuMDI4LS4wMTMgNC45NzYtLjA0OC45NDctLjAzNCAxLjUyMS0uMTU2IDIuMDA1LS4zNGEzLjg5MiAzLjg5MiAwIDAgMCAxLjQwNy0uOTIzIDMuODkyIDMuODkyIDAgMCAwIC45MjMtMS40MDdjLjE4NC0uNDg0LjMwNi0xLjA1OC4zNC0yLjAwNS4wMzUtLjk0OC4wNDgtMS4zNTUuMDQ4LTQuOTc2VjkuNzAxYzAtMy42MjEtLjAxMy00LjAyOC0uMDQ4LTQuOTc2LS4wMzQtLjk0Ny0uMTU2LTEuNTIxLS4zNC0yLjAwNWEzLjg5MiAzLjg5MiAwIDAgMC0uOTIzLTEuNDA3QTMuODkyIDMuODkyIDAgMCAwIDE5Ljk4MiAyLjcyYy0uNDg0LS4xODQtMS4wNTgtLjMwNi0yLjAwNS0uMzRDMTcuMDI5LjAxMyAxNi42MjIgMCAxMy4wMDEgMGgtMS45ODR6bS0uNzE3IDEuNDQyaDIuMDA0YzMuMTM2IDAgMy41MDYuMDExIDQuNzIuMDQyLjg3OC4wMzQgMS4zNTQuMTU1IDEuNjcxLjI1Ny40Mi4xNjMuNzIuMzU4IDEuMDM1LjY3My4zMTUuMzE1LjUxLjYxNS42NzMgMS4wMzUuMTAyLjMxNy4yMjMuNzkzLjI1NyAxLjY3MS4wMzEgMS4yMTQuMDQyIDEuNTg0LjA0MiA0Ljcydi43MTdjMCAzLjEzNi0uMDExIDMuNTA2LS4wNDIgNC43Mi0uMDM0Ljg3OC0uMTU1IDEuMzU0LS4yNTcgMS42NzFhMi40NSAyLjQ1IDAgMCAxLS42NzMgMS4wMzUgMi40NSAyLjQ1IDAgMCAxLTEuMDM1LjY3M2MtLjMxNy4xMDItLjc5My4yMjMtMS42NzEuMjU3LTEuMjE0LjAzMS0xLjU4NC4wNDItNC43Mi4wNDJIOS4yODNjLTMuMTM2IDAtMy41MDYtLjAxMS00LjcyLS4wNDItLjg3OC0uMDM0LTEuMzU0LS4xNTUtMS42NzEtLjI1N2EyLjQ1IDIuNDUgMCAwIDEtMS4wMzUtLjY3MyAyLjQ1IDIuNDUgMCAwIDEtLjY3My0xLjAzNWMtLjEwMi0uMzE3LS4yMjMtLjc5My0uMjU3LTEuNjcxLS4wMzEtMS4yMTQtLjA0Mi0xLjU4NC0uMDQyLTQuNzJWOS4yODNjMC0zLjEzNi4wMTEtMy41MDYuMDQyLTQuNzIuMDM0LS44NzguMTU1LTEuMzU0LjI1Ny0xLjY3MS4xNjMtLjQyLjM1OC0uNzIuNjczLTEuMDM1YTIuNDUgMi40NSAwIDAgMSAxLjAzNS0uNjczYy4zMTctLjEwMi43OTMtLjIyMyAxLjY3MS0uMjU3IDEuMjE0LS4wMzEgMS41ODQtLjA0MiA0LjcyLS4wNDJ6XCJcbiAgICAgICAgICAgIGNsaXBSdWxlPVwiZXZlbm9kZFwiXG4gICAgICAgICAgLz5cbiAgICAgICAgPC9zdmc+XG4gICAgICApLFxuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogJ0xpbmtlZEluJyxcbiAgICAgIGhyZWY6ICcjJyxcbiAgICAgIGljb246IChwcm9wczogYW55KSA9PiAoXG4gICAgICAgIDxzdmcgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIiB7Li4ucHJvcHN9PlxuICAgICAgICAgIDxwYXRoIGQ9XCJNMjAuNDQ3IDIwLjQ1MmgtMy41NTR2LTUuNTY5YzAtMS4zMjgtLjAyNy0zLjAzNy0xLjg1Mi0zLjAzNy0xLjg1MyAwLTIuMTM2IDEuNDQ1LTIuMTM2IDIuOTM5djUuNjY3SDkuMzUxVjloMy40MTR2MS41NjFoLjA0NmMuNDc3LS45IDEuNjM3LTEuODUgMy4zNy0xLjg1IDMuNjAxIDAgNC4yNjcgMi4zNyA0LjI2NyA1LjQ1NXY2LjI4NnpNNS4zMzcgNy40MzNjLTEuMTQ0IDAtMi4wNjMtLjkyNi0yLjA2My0yLjA2NSAwLTEuMTM4LjkyLTIuMDYzIDIuMDYzLTIuMDYzIDEuMTQgMCAyLjA2NC45MjUgMi4wNjQgMi4wNjMgMCAxLjEzOS0uOTI1IDIuMDY1LTIuMDY0IDIuMDY1em0xLjc4MiAxMy4wMTlIMy41NTVWOWgzLjU2NHYxMS40NTJ6TTIyLjIyNSAwSDEuNzcxQy43OTIgMCAwIC43NzQgMCAxLjcyOXYyMC41NDJDMCAyMy4yMjcuNzkyIDI0IDEuNzcxIDI0aDIwLjQ1MUMyMy4yIDI0IDI0IDIzLjIyNyAyNCAyMi4yNzFWMS43MjlDMjQgLjc3NCAyMy4yIDAgMjIuMjIyIDBoLjAwM3pcIi8+XG4gICAgICAgIDwvc3ZnPlxuICAgICAgKSxcbiAgICB9LFxuICBdLFxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBGb290ZXIoKSB7XG4gIHJldHVybiAoXG4gICAgPGZvb3RlciBjbGFzc05hbWU9XCJiZy1ncmF5LTkwMFwiIGFyaWEtbGFiZWxsZWRieT1cImZvb3Rlci1oZWFkaW5nXCI+XG4gICAgICA8aDIgaWQ9XCJmb290ZXItaGVhZGluZ1wiIGNsYXNzTmFtZT1cInNyLW9ubHlcIj5cbiAgICAgICAgRm9vdGVyXG4gICAgICA8L2gyPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJteC1hdXRvIG1heC13LTd4bCBweC02IHBiLTggcHQtMTYgc206cHQtMjQgbGc6cHgtOCBsZzpwdC0zMlwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInhsOmdyaWQgeGw6Z3JpZC1jb2xzLTMgeGw6Z2FwLThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktOFwiPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtd2hpdGVcIj5USyBOdXJ0dXJlPC9zcGFuPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS0zMDAgbXQtMlwiPlRyb3VwICYgS291YmVrPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGxlYWRpbmctNiB0ZXh0LWdyYXktMzAwXCI+XG4gICAgICAgICAgICAgIFNwZWNpYWxpenVqZW1lIHNlIG5hIGtvbXBsZXhuw60gbWFya2V0aW5nb3bDqSBzbHXFvmJ5IHBybyBtYWzDqSBhIHN0xZllZG7DrSBwb2RuaWt5LiBcbiAgICAgICAgICAgICAgUG9tw6Fow6FtZSB2w6FtIHLFr3N0IHMgbWluaW3DoWxuw61taSBzdGFyb3N0bWkgYSBtYXhpbcOhbG7DrSBlZmVrdGl2aXRvdS5cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTZcIj5cbiAgICAgICAgICAgICAge25hdmlnYXRpb24uc29jaWFsLm1hcCgoaXRlbSkgPT4gKFxuICAgICAgICAgICAgICAgIDxhIGtleT17aXRlbS5uYW1lfSBocmVmPXtpdGVtLmhyZWZ9IGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwic3Itb25seVwiPntpdGVtLm5hbWV9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPGl0ZW0uaWNvbiBjbGFzc05hbWU9XCJoLTYgdy02XCIgYXJpYS1oaWRkZW49XCJ0cnVlXCIgLz5cbiAgICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0xNiBncmlkIGdyaWQtY29scy0yIGdhcC04IHhsOmNvbC1zcGFuLTIgeGw6bXQtMFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtZDpncmlkIG1kOmdyaWQtY29scy0yIG1kOmdhcC04XCI+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1zZW1pYm9sZCBsZWFkaW5nLTYgdGV4dC13aGl0ZVwiPlNsdcW+Ynk8L2gzPlxuICAgICAgICAgICAgICAgIDx1bCByb2xlPVwibGlzdFwiIGNsYXNzTmFtZT1cIm10LTYgc3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICB7bmF2aWdhdGlvbi5zZXJ2aWNlcy5tYXAoKGl0ZW0pID0+IChcbiAgICAgICAgICAgICAgICAgICAgPGxpIGtleT17aXRlbS5uYW1lfT5cbiAgICAgICAgICAgICAgICAgICAgICA8TGluayBocmVmPXtpdGVtLmhyZWZ9IGNsYXNzTmFtZT1cInRleHQtc20gbGVhZGluZy02IHRleHQtZ3JheS0zMDAgaG92ZXI6dGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge2l0ZW0ubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L3VsPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0xMCBtZDptdC0wXCI+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1zZW1pYm9sZCBsZWFkaW5nLTYgdGV4dC13aGl0ZVwiPlNwb2xlxI1ub3N0PC9oMz5cbiAgICAgICAgICAgICAgICA8dWwgcm9sZT1cImxpc3RcIiBjbGFzc05hbWU9XCJtdC02IHNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAge25hdmlnYXRpb24uY29tcGFueS5tYXAoKGl0ZW0pID0+IChcbiAgICAgICAgICAgICAgICAgICAgPGxpIGtleT17aXRlbS5uYW1lfT5cbiAgICAgICAgICAgICAgICAgICAgICA8TGluayBocmVmPXtpdGVtLmhyZWZ9IGNsYXNzTmFtZT1cInRleHQtc20gbGVhZGluZy02IHRleHQtZ3JheS0zMDAgaG92ZXI6dGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge2l0ZW0ubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L3VsPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtZDpncmlkIG1kOmdyaWQtY29scy0xIG1kOmdhcC04XCI+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1zZW1pYm9sZCBsZWFkaW5nLTYgdGV4dC13aGl0ZVwiPktvbnRha3Q8L2gzPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNiBzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgICAgICAgICAgICA8c3Ryb25nPkVtYWlsOjwvc3Ryb25nPiBpbmZvQHRrbnVydHVyZS5jelxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktMzAwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzdHJvbmc+VGVsZWZvbjo8L3N0cm9uZz4gKzQyMCAxMjMgNDU2IDc4OVxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktMzAwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzdHJvbmc+QWRyZXNhOjwvc3Ryb25nPiBQcmFoYSwgxIxlc2vDoSByZXB1Ymxpa2FcbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMTYgYm9yZGVyLXQgYm9yZGVyLWdyYXktOTAwLzEwIHB0LTggc206bXQtMjAgbGc6bXQtMjRcIj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIGxlYWRpbmctNSB0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAmY29weTsgMjAyNCBUSyBOdXJ0dXJlIChUcm91cCAmIEtvdWJlaykuIFbFoWVjaG5hIHByw6F2YSB2eWhyYXplbmEuXG4gICAgICAgICAgPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZm9vdGVyPlxuICApXG59XG4iXSwibmFtZXMiOlsiTGluayIsIm5hdmlnYXRpb24iLCJzZXJ2aWNlcyIsIm5hbWUiLCJocmVmIiwiY29tcGFueSIsInNvY2lhbCIsImljb24iLCJwcm9wcyIsInN2ZyIsImZpbGwiLCJ2aWV3Qm94IiwicGF0aCIsImZpbGxSdWxlIiwiZCIsImNsaXBSdWxlIiwiRm9vdGVyIiwiZm9vdGVyIiwiY2xhc3NOYW1lIiwiYXJpYS1sYWJlbGxlZGJ5IiwiaDIiLCJpZCIsImRpdiIsInNwYW4iLCJwIiwibWFwIiwiaXRlbSIsImEiLCJhcmlhLWhpZGRlbiIsImgzIiwidWwiLCJyb2xlIiwibGkiLCJzdHJvbmciXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\new web\\tk-nurture-website\\src\\components\\Header.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Header.tsx */ \"(ssr)/./src/components/Header.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NwYWprJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q25ldyUyMHdlYiU1QyU1Q3RrLW51cnR1cmUtd2Vic2l0ZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1wYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NwYWprJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q25ldyUyMHdlYiU1QyU1Q3RrLW51cnR1cmUtd2Vic2l0ZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1zZWdtZW50LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NwYWprJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q25ldyUyMHdlYiU1QyU1Q3RrLW51cnR1cmUtd2Vic2l0ZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NwYWprJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q25ldyUyMHdlYiU1QyU1Q3RrLW51cnR1cmUtd2Vic2l0ZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2h0dHAtYWNjZXNzLWZhbGxiYWNrJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDc3BhamslNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDbmV3JTIwd2ViJTVDJTVDdGstbnVydHVyZS13ZWJzaXRlJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbGF5b3V0LXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNzcGFqayU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNuZXclMjB3ZWIlNUMlNUN0ay1udXJ0dXJlLXdlYnNpdGUlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NwYWprJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q25ldyUyMHdlYiU1QyU1Q3RrLW51cnR1cmUtd2Vic2l0ZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q21ldGFkYXRhJTVDJTVDbWV0YWRhdGEtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDc3BhamslNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDbmV3JTIwd2ViJTVDJTVDdGstbnVydHVyZS13ZWJzaXRlJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb09BQTZLO0FBQzdLO0FBQ0EsME9BQWdMO0FBQ2hMO0FBQ0EsME9BQWdMO0FBQ2hMO0FBQ0Esb1JBQXNNO0FBQ3RNO0FBQ0Esd09BQStLO0FBQy9LO0FBQ0EsNFBBQTBMO0FBQzFMO0FBQ0Esa1FBQTZMO0FBQzdMO0FBQ0Esc1FBQThMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxzcGFqa1xcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxuZXcgd2ViXFxcXHRrLW51cnR1cmUtd2Vic2l0ZVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxzcGFqa1xcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxuZXcgd2ViXFxcXHRrLW51cnR1cmUtd2Vic2l0ZVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1zZWdtZW50LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxzcGFqa1xcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxuZXcgd2ViXFxcXHRrLW51cnR1cmUtd2Vic2l0ZVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxzcGFqa1xcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxuZXcgd2ViXFxcXHRrLW51cnR1cmUtd2Vic2l0ZVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGh0dHAtYWNjZXNzLWZhbGxiYWNrXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxzcGFqa1xcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxuZXcgd2ViXFxcXHRrLW51cnR1cmUtd2Vic2l0ZVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGxheW91dC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHNwYWprXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXG5ldyB3ZWJcXFxcdGstbnVydHVyZS13ZWJzaXRlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbWV0YWRhdGFcXFxcYXN5bmMtbWV0YWRhdGEuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHNwYWprXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXG5ldyB3ZWJcXFxcdGstbnVydHVyZS13ZWJzaXRlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbWV0YWRhdGFcXFxcbWV0YWRhdGEtYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHNwYWprXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXG5ldyB3ZWJcXFxcdGstbnVydHVyZS13ZWJzaXRlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Csrc%5C%5Capp%5C%5Ckontakt%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Csrc%5C%5Capp%5C%5Ckontakt%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/kontakt/page.tsx */ \"(ssr)/./src/app/kontakt/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NwYWprJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q25ldyUyMHdlYiU1QyU1Q3RrLW51cnR1cmUtd2Vic2l0ZSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2tvbnRha3QlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0tBQTRJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxzcGFqa1xcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxuZXcgd2ViXFxcXHRrLW51cnR1cmUtd2Vic2l0ZVxcXFxzcmNcXFxcYXBwXFxcXGtvbnRha3RcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cspajk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cnew%20web%5C%5Ctk-nurture-website%5C%5Csrc%5C%5Capp%5C%5Ckontakt%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/kontakt/page.tsx":
/*!**********************************!*\
  !*** ./src/app/kontakt/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Kontakt)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,ClockIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,ClockIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,ClockIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,ClockIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,ClockIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,ClockIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,ClockIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst contactInfo = [\n    {\n        icon: _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: 'Email',\n        details: '<EMAIL>',\n        description: 'Odpovídáme do 2 hodin'\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: 'Telefon',\n        details: '+420 123 456 789',\n        description: 'Po-Pá 9:00-18:00'\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: 'Adresa',\n        details: 'Praha, Česká republika',\n        description: 'Osobní schůzky po domluvě'\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: 'Rychlá odpověď',\n        details: '30 minut',\n        description: 'Průměrná doba odezvy'\n    }\n];\nconst services = [\n    'Content distribuce',\n    'Tvorba contentu',\n    'Web & CRM automatizace',\n    'SEO optimalizace',\n    'Social media management',\n    'Kompletní marketingové řešení'\n];\nfunction Kontakt() {\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        email: '',\n        company: '',\n        phone: '',\n        service: '',\n        message: '',\n        budget: ''\n    });\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        // Handle form submission here\n        console.log('Form submitted:', formData);\n        alert('Děkujeme za vaši zprávu! Ozveme se vám do 2 hodin.');\n    };\n    const handleChange = (e)=>{\n        setFormData({\n            ...formData,\n            [e.target.name]: e.target.value\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative isolate px-6 pt-24 lg:px-8 hero-bg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 -z-10 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full blur-3xl float-animation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-3/4 right-1/4 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-cyan-400/20 rounded-full blur-3xl float-animation\",\n                                style: {\n                                    animationDelay: '2s'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto max-w-4xl py-32 sm:py-48 lg:py-56 px-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-flex items-center gap-2 px-6 py-3 rounded-full bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-200/50 mb-8 glass\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-semibold text-purple-800\",\n                                            children: \"Začněme spolupr\\xe1ci\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold tracking-tight text-slate-900 sm:text-6xl lg:text-7xl font-space leading-tight\",\n                                    children: [\n                                        \"Kontaktujte \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"gradient-text\",\n                                            children: \"TK Nurture\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 27\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-8 text-lg sm:text-xl leading-relaxed text-slate-600 max-w-3xl mx-auto\",\n                                    children: \"Z\\xedskejte bezplatnou konzultaci a strategick\\xfd pl\\xe1n pro v\\xe1š podnik. Během 30 minut v\\xe1m uk\\xe1žeme, jak můžete zdvojn\\xe1sobit svůj online dosah.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"py-24 sm:py-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-7xl px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold tracking-tight text-slate-900 sm:text-4xl font-space mb-8\",\n                                        children: [\n                                            \"Z\\xedskejte \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"gradient-text\",\n                                                children: \"bezplatnou konzultaci\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 26\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSubmit,\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"name\",\n                                                                className: \"block text-sm font-medium text-slate-700 mb-2\",\n                                                                children: \"Jm\\xe9no a př\\xedjmen\\xed *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                                lineNumber: 117,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: \"name\",\n                                                                name: \"name\",\n                                                                required: true,\n                                                                value: formData.name,\n                                                                onChange: handleChange,\n                                                                className: \"w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors\",\n                                                                placeholder: \"Jan Nov\\xe1k\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                                lineNumber: 120,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"email\",\n                                                                className: \"block text-sm font-medium text-slate-700 mb-2\",\n                                                                children: \"Email *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                                lineNumber: 133,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"email\",\n                                                                id: \"email\",\n                                                                name: \"email\",\n                                                                required: true,\n                                                                value: formData.email,\n                                                                onChange: handleChange,\n                                                                className: \"w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors\",\n                                                                placeholder: \"<EMAIL>\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                                lineNumber: 136,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"company\",\n                                                                className: \"block text-sm font-medium text-slate-700 mb-2\",\n                                                                children: \"N\\xe1zev firmy\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                                lineNumber: 151,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: \"company\",\n                                                                name: \"company\",\n                                                                value: formData.company,\n                                                                onChange: handleChange,\n                                                                className: \"w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors\",\n                                                                placeholder: \"Moje firma s.r.o.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                                lineNumber: 154,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"phone\",\n                                                                className: \"block text-sm font-medium text-slate-700 mb-2\",\n                                                                children: \"Telefon\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"tel\",\n                                                                id: \"phone\",\n                                                                name: \"phone\",\n                                                                value: formData.phone,\n                                                                onChange: handleChange,\n                                                                className: \"w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors\",\n                                                                placeholder: \"+420 123 456 789\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                                lineNumber: 169,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"service\",\n                                                                className: \"block text-sm font-medium text-slate-700 mb-2\",\n                                                                children: \"Jak\\xe1 služba v\\xe1s zaj\\xedm\\xe1?\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                                lineNumber: 183,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                id: \"service\",\n                                                                name: \"service\",\n                                                                value: formData.service,\n                                                                onChange: handleChange,\n                                                                className: \"w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: \"Vyberte službu\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                                        lineNumber: 193,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    services.map((service)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: service,\n                                                                            children: service\n                                                                        }, service, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                                            lineNumber: 195,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                                lineNumber: 186,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"budget\",\n                                                                className: \"block text-sm font-medium text-slate-700 mb-2\",\n                                                                children: \"Měs\\xedčn\\xed rozpočet\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                id: \"budget\",\n                                                                name: \"budget\",\n                                                                value: formData.budget,\n                                                                onChange: handleChange,\n                                                                className: \"w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: \"Vyberte rozpočet\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                                        lineNumber: 211,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"10-25k\",\n                                                                        children: \"10 000 - 25 000 Kč\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                                        lineNumber: 212,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"25-50k\",\n                                                                        children: \"25 000 - 50 000 Kč\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                                        lineNumber: 213,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"50-100k\",\n                                                                        children: \"50 000 - 100 000 Kč\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                                        lineNumber: 214,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"100k+\",\n                                                                        children: \"100 000+ Kč\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                                        lineNumber: 215,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"message\",\n                                                        className: \"block text-sm font-medium text-slate-700 mb-2\",\n                                                        children: \"Zpr\\xe1va\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        id: \"message\",\n                                                        name: \"message\",\n                                                        rows: 4,\n                                                        value: formData.message,\n                                                        onChange: handleChange,\n                                                        className: \"w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors\",\n                                                        placeholder: \"Popište n\\xe1m vaše potřeby a c\\xedle...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                className: \"w-full group relative px-8 py-4 text-lg font-semibold text-white rounded-2xl btn-futuristic neon-glow overflow-hidden shadow-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-5 h-5 inline mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Z\\xedskat bezplatnou konzultaci\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 shimmer-effect opacity-0 group-hover:opacity-100 transition-opacity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold tracking-tight text-slate-900 sm:text-4xl font-space mb-8\",\n                                        children: [\n                                            \"Kontaktn\\xed \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"gradient-text\",\n                                                children: \"informace\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 27\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6 mb-12\",\n                                        children: contactInfo.map((info, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start gap-4 p-6 rounded-2xl bg-white shadow-lg border border-slate-200/50 hover:border-purple-200/50 transition-all duration-300\",\n                                                style: {\n                                                    animationDelay: `${index * 0.1}s`\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 rounded-xl bg-gradient-to-br from-purple-500 to-pink-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(info.icon, {\n                                                            className: \"h-6 w-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-semibold text-slate-900 mb-1\",\n                                                                children: info.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                                lineNumber: 263,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg font-medium text-slate-700 mb-1\",\n                                                                children: info.details\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                                lineNumber: 264,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-slate-500\",\n                                                                children: info.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, info.title, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-8 rounded-3xl bg-gradient-to-br from-slate-900 to-slate-800 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold mb-6\",\n                                                children: \"Co můžete oček\\xe1vat?\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"h-5 w-5 text-green-400 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Bezplatnou 30minutovou konzultaci\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"h-5 w-5 text-green-400 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Anal\\xfdzu vašich současn\\xfdch procesů\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"h-5 w-5 text-green-400 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Strategick\\xfd pl\\xe1n pro růst\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"h-5 w-5 text-green-400 flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Konkr\\xe9tn\\xed doporučen\\xed a next steps\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-8 p-4 rounded-xl bg-white/5 backdrop-blur-sm\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-slate-300\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            className: \"text-white\",\n                                                            children: \"Garance:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Pokud během 90 dnů neuvid\\xedte zlepšen\\xed, vr\\xe1t\\xedme v\\xe1m 100% investice.\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"py-16 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/\",\n                    className: \"group inline-flex items-center gap-2 px-8 py-4 text-lg font-medium text-slate-700 hover:text-purple-600 transition-colors border border-slate-300 rounded-2xl hover:border-purple-300\",\n                    children: [\n                        \"Zpět na hlavn\\xed str\\xe1nku\",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-4 h-4 group-hover:translate-x-1 transition-transform\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                    lineNumber: 307,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n                lineNumber: 306,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\app\\\\kontakt\\\\page.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/kontakt/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst navigation = [\n    {\n        name: 'Domů',\n        href: '/'\n    },\n    {\n        name: 'Content distribuce',\n        href: '/content-distribuce'\n    },\n    {\n        name: 'Tvorba contentu',\n        href: '/tvorba-contentu'\n    },\n    {\n        name: 'Web & CRM',\n        href: '/web-crm'\n    },\n    {\n        name: 'Implementace',\n        href: '/implementace'\n    },\n    {\n        name: 'O nás',\n        href: '/o-nas'\n    },\n    {\n        name: 'Kontakt',\n        href: '/kontakt'\n    }\n];\nfunction Header() {\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"glass sticky top-0 z-50 border-b border-white/10\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"mx-auto flex max-w-7xl items-center justify-between p-6 lg:px-8\",\n                \"aria-label\": \"Global\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex lg:flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"-m-1.5 p-1.5 group\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-lg\",\n                                            children: \"TK\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 27,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xl font-bold gradient-text font-space\",\n                                                children: \"TK Nurture\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 30,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-slate-500\",\n                                                children: \"Troup & Koubek\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 31,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 29,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex lg:hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            className: \"-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700\",\n                            onClick: ()=>setMobileMenuOpen(true),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"Otevř\\xedt hlavn\\xed menu\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-6 w-6\",\n                                    \"aria-hidden\": \"true\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden lg:flex lg:gap-x-8\",\n                        children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: item.href,\n                                className: \"relative px-3 py-2 text-sm font-bold text-slate-900 hover:text-purple-600 transition-colors group\",\n                                children: [\n                                    item.name,\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-purple-500 to-pink-500 group-hover:w-full transition-all duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, item.name, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden lg:flex lg:flex-1 lg:justify-end\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/kontakt\",\n                            className: \"relative px-6 py-3 text-sm font-semibold text-white rounded-xl btn-futuristic overflow-hidden group\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"relative z-10\",\n                                children: \"Z\\xedskat nab\\xeddku\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-50\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        className: \"-m-1.5 p-1.5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold text-primary-600\",\n                                            children: \"TK Nurture\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"-m-2.5 rounded-md p-2.5 text-gray-700\",\n                                        onClick: ()=>setMobileMenuOpen(false),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Zavř\\xedt menu\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-6 w-6\",\n                                                \"aria-hidden\": \"true\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 flow-root\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"-my-6 divide-y divide-gray-500/10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 py-6\",\n                                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: item.href,\n                                                    className: \"-mx-3 block rounded-lg px-3 py-2 text-base font-bold leading-7 text-slate-900 hover:bg-gray-50\",\n                                                    onClick: ()=>setMobileMenuOpen(false),\n                                                    children: item.name\n                                                }, item.name, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"py-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/kontakt\",\n                                                className: \"bg-primary-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-primary-700 block text-center\",\n                                                onClick: ()=>setMobileMenuOpen(false),\n                                                children: \"Z\\xedskat nab\\xeddku\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 70,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\new web\\\\tk-nurture-website\\\\src\\\\components\\\\Header.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@heroicons","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fkontakt%2Fpage&page=%2Fkontakt%2Fpage&appPaths=%2Fkontakt%2Fpage&pagePath=private-next-app-dir%2Fkontakt%2Fpage.tsx&appDir=C%3A%5CUsers%5Cspajk%5CDocuments%5Caugment-projects%5Cnew%20web%5Ctk-nurture-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cspajk%5CDocuments%5Caugment-projects%5Cnew%20web%5Ctk-nurture-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();